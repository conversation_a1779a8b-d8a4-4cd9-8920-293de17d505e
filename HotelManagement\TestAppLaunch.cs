using System;
using System.Windows.Forms;
using System.Configuration;
using System.IO;
using System.Data.SqlClient;

namespace HotelManagement
{
    class TestAppLaunch
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                MessageBox.Show("Application started in diagnostic mode.", "Diagnostic Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // Test config file access
                try
                {
                    string connStr = ConfigurationManager.ConnectionStrings["HotelDB"].ConnectionString;
                    MessageBox.Show("Connection string found: " + connStr, "Config Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception configEx)
                {
                    MessageBox.Show("Error reading config: " + configEx.Message, "Config Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                
                // Test database access
                try
                {
                    string connStr = ConfigurationManager.ConnectionStrings["HotelDB"].ConnectionString;
                    using (SqlConnection conn = new SqlConnection(connStr))
                    {
                        conn.Open();
                        MessageBox.Show("Database connection successful!", "DB Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception dbEx)
                {
                    MessageBox.Show("Database error: " + dbEx.Message, "DB Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                
                // Check if all required files exist
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string[] requiredFiles = new string[] 
                {
                    "HotelManagement.exe.config",
                    "FontAwesome.Sharp.dll"
                };
                
                foreach (string file in requiredFiles)
                {
                    string path = Path.Combine(baseDir, file);
                    if (!File.Exists(path))
                    {
                        MessageBox.Show("Required file missing: " + file, "File Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                
                MessageBox.Show("Diagnostic tests completed.", "Diagnostic Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Critical error in diagnostic: " + ex.ToString(), "Critical Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
} 