<?php
// Database configuration for KHQR integration with Hotel Management System
// Uses MySQL/MariaDB for XAMPP environment

$host = "localhost";
$database = "hotel_khqr";
$username = "root";
$password = "";

try {
    // Create MySQL connection
    $conn = new mysqli($host, $username, $password, $database);

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Set charset
    $conn->set_charset("utf8");

    // Create tables if they don't exist
    createTables($conn);

} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

function createTables($conn) {
    // Create qrcode_data table for KHQR transactions
    $sql = "CREATE TABLE IF NOT EXISTS qrcode_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        qr_data TEXT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        md5 VARCHAR(32) NOT NULL UNIQUE,
        short_hash VARCHAR(8),
        username VARCHAR(100),
        profile_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        transaction_id VARCHAR(50),
        status ENUM('pending', 'completed', 'failed', 'expired') DEFAULT 'pending',
        ip_address VARCHAR(45),
        INDEX idx_md5 (md5),
        INDEX idx_profile_id (profile_id),
        INDEX idx_status (status)
    )";
    $conn->query($sql);
    
    // Create users table for profile management
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        profile_id VARCHAR(50) UNIQUE,
        bakong_id VARCHAR(100),
        bakong_name VARCHAR(100),
        company_name VARCHAR(100),
        api_expiry_date DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_profile_id (profile_id)
    )";
    $conn->query($sql);
    
    // Create payments table for payment tracking
    $sql = "CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        amount DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        transaction_id VARCHAR(50),
        md5 VARCHAR(32),
        remark TEXT,
        payment_method VARCHAR(50),
        date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME,
        payer_account VARCHAR(100),
        short_hash VARCHAR(8),
        FOREIGN KEY (user_id) REFERENCES users(id),
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_md5 (md5),
        INDEX idx_status (status)
    )";
    $conn->query($sql);
    
    // Create hotel_bookings table to sync with C# application
    $sql = "CREATE TABLE IF NOT EXISTS hotel_bookings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT UNIQUE,
        guest_name VARCHAR(100),
        room_number VARCHAR(20),
        amount DECIMAL(10,2),
        status VARCHAR(50),
        khqr_payment_status ENUM('none', 'pending', 'completed', 'failed') DEFAULT 'none',
        khqr_transaction_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_booking_id (booking_id),
        INDEX idx_khqr_status (khqr_payment_status)
    )";
    $conn->query($sql);
}

// Function to execute MySQL queries
function executeQuery($query, $params = [], $types = '') {
    global $conn;

    $stmt = $conn->prepare($query);
    if ($params && $types) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    return $stmt;
}

// Function to get booking information
function getBookingInfo($bookingId) {
    global $conn;

    try {
        // Check if booking exists in sync table
        $query = "SELECT * FROM hotel_bookings WHERE booking_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $bookingId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            return $row;
        }
    } catch (Exception $e) {
        error_log("Error getting booking info: " . $e->getMessage());
    }

    return null;
}

// Function to update booking payment status
function updateBookingPaymentStatus($bookingId, $status, $transactionId = null) {
    global $conn;

    try {
        $query = "UPDATE hotel_bookings SET khqr_payment_status = ?, khqr_transaction_id = ?, updated_at = NOW() WHERE booking_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ssi", $status, $transactionId, $bookingId);
        $stmt->execute();

        return true;
    } catch (Exception $e) {
        error_log("Error updating booking payment status: " . $e->getMessage());
        return false;
    }
}

?>
