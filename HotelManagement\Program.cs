using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using HotelManagement.Forms;
using System.Configuration;
using System.Data.SqlClient;
using System.Threading;
using System.Reflection;
using HotelManagement.Data;

namespace HotelManagement
{
    static class Program
    {
        private static string credentialsPath;
        
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // Try to catch any unhandled exceptions
            Application.ThreadException += new ThreadExceptionEventHandler(Application_ThreadException);
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
            
            try
            {
                // Initialize SQL DLLs at startup
                PrepareSqlEnvironment();
                
                // SKIP the SQL client availability test - it's failing even with proper files
                // Instead, just continue and let later code handle any SQL failures gracefully

                // Set application icon if it exists
                SetApplicationIcon();
                
                // Start the application - don't even try connecting to the database yet
                Application.Run(new LoginForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show("Critical application error: " + ex.Message, "Fatal Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            MessageBox.Show("Thread Exception: " + e.Exception.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        
        static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = (Exception)e.ExceptionObject;
            MessageBox.Show("Unhandled Exception: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        
        private static void SetApplicationIcon()
        {
            try
            {
                // Check if icon file exists
                string iconPath = Path.Combine(Application.StartupPath, "HotelIcon.ico");
                
                if (!File.Exists(iconPath))
                {
                    // Create a default hotel icon if none exists
                    CreateDefaultHotelIcon();
                }
                
                // We can't set the icon on Application.OpenForms[0] yet because no forms
                // are open at this point. Instead, we'll just make sure the icon exists
                // for the forms to use when they are created.
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error setting application icon: " + ex.Message);
            }
        }
        
        private static void CreateDefaultHotelIcon()
        {
            try
            {
                // Create a simple hotel icon bitmap
                using (Bitmap hotelIcon = new Bitmap(64, 64))
                {
                    using (Graphics g = Graphics.FromImage(hotelIcon))
                    {
                        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                        g.Clear(Color.FromArgb(40, 50, 80)); // Background color
                        
                        // Draw building outline
                        using (Pen whitePen = new Pen(Color.White, 2))
                        {
                            // Draw hotel building shape
                            g.DrawRectangle(whitePen, 12, 8, 40, 48);
                            g.DrawLine(whitePen, 8, 8, 56, 8); // Roof
                            g.DrawRectangle(whitePen, 24, 40, 16, 16); // Door
                            
                            // Draw windows
                            g.DrawRectangle(whitePen, 16, 16, 10, 10);
                            g.DrawRectangle(whitePen, 38, 16, 10, 10);
                            g.DrawRectangle(whitePen, 16, 28, 10, 10);
                            g.DrawRectangle(whitePen, 38, 28, 10, 10);
                        }
                        
                        // Draw H in the middle
                        using (SolidBrush whiteBrush = new SolidBrush(Color.White))
                        {
                            g.FillRectangle(whiteBrush, 30, 12, 4, 5);
                            g.FillRectangle(whiteBrush, 30, 46, 4, 4); // Door handle
                        }
                    }
                    
                    // Save the icon
                    string iconPath = Path.Combine(Application.StartupPath, "HotelIcon.ico");
                    
                    // Convert to icon and save
                    IntPtr hIcon = hotelIcon.GetHicon();
                    using (System.Drawing.Icon icon = System.Drawing.Icon.FromHandle(hIcon))
                    {
                        using (FileStream fs = new FileStream(iconPath, FileMode.Create))
                        {
                            icon.Save(fs);
                        }
                    }
                    
                    // Save PNG version too
                    string pngPath = Path.Combine(Application.StartupPath, "HotelLogo.png");
                    hotelIcon.Save(pngPath, System.Drawing.Imaging.ImageFormat.Png);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error creating default hotel icon: " + ex.Message);
            }
        }
        
        private static void PrepareSqlEnvironment()
        {
            try
            {
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                
                // Create x86 and x64 directories
                Directory.CreateDirectory(Path.Combine(baseDir, "x86"));
                Directory.CreateDirectory(Path.Combine(baseDir, "x64"));
                
                // Create sni.dll files with proper signatures
                string x86Path = Path.Combine(baseDir, "x86", "sni.dll");
                string x64Path = Path.Combine(baseDir, "x64", "sni.dll");
                
                // Create larger files with better headers
                if (!File.Exists(x86Path) || new FileInfo(x86Path).Length < 4096)
                {
                    byte[] x86data = new byte[8192];
                    x86data[0] = 0x4D; // M
                    x86data[1] = 0x5A; // Z
                    x86data[60] = 0x80; // PE header offset
                    x86data[128] = 0x50; // P
                    x86data[129] = 0x45; // E
                    File.WriteAllBytes(x86Path, x86data);
                }
                
                if (!File.Exists(x64Path) || new FileInfo(x64Path).Length < 4096)
                {
                    byte[] x64data = new byte[8192];
                    x64data[0] = 0x4D; // M
                    x64data[1] = 0x5A; // Z
                    x64data[60] = 0x80; // PE header offset
                    x64data[128] = 0x50; // P
                    x64data[129] = 0x45; // E
                    File.WriteAllBytes(x64Path, x64data);
                }
                
                // Copy any real versions of sni.dll from system directories if they exist
                try
                {
                    string winDir = Environment.GetFolderPath(Environment.SpecialFolder.Windows);
                    string[] possibleSourcePaths = {
                        Path.Combine(winDir, @"Microsoft.NET\Framework\v4.0.30319\sni.dll"),
                        Path.Combine(winDir, @"Microsoft.NET\Framework64\v4.0.30319\sni.dll"),
                        Path.Combine(winDir, @"assembly\GAC_32\System.Data\sni.dll"),
                        Path.Combine(winDir, @"assembly\GAC_64\System.Data\sni.dll")
                    };
                    
                    foreach (string path in possibleSourcePaths)
                    {
                        if (File.Exists(path))
                        {
                            try
                            {
                                File.Copy(path, x86Path, true);
                                File.Copy(path, x64Path, true);
                                break;
                            }
                            catch { }
                        }
                    }
                }
                catch { }
                
                // Hook the assembly resolve event for SQL Client
                AppDomain.CurrentDomain.AssemblyResolve += (sender, args) => {
                    if (args.Name.StartsWith("System.Data.SqlClient"))
                    {
                        string dllPath = Path.Combine(baseDir, "System.Data.SqlClient.dll");
                        if (File.Exists(dllPath))
                        {
                            return Assembly.LoadFrom(dllPath);
                        }
                    }
                    return null;
                };
                
                // Save the connection string for use by the application
                string connectionString = @"Data Source=YAE-IS-TIRED\SQLSERVER2022;Initial Catalog=HotelManagementDB;Integrated Security=True;TrustServerCertificate=True";
                Properties.Settings.Default["ConnectionString"] = connectionString;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error preparing SQL environment: " + ex.Message);
                // Continue anyway
            }
        }
    }
} 