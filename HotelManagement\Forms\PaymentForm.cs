using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Net.Http;
using System.Threading.Tasks;
using HotelManagement.Data;
using HotelManagement.Models;

namespace HotelManagement.Forms
{
    public partial class PaymentForm : Form
    {
        private readonly DatabaseManager _dbManager;
        private List<Booking> _unpaidBookings;
        private Booking _selectedBooking;
        private decimal _amountDue;
        private const decimal KHR_EXCHANGE_RATE = 4050; // 1 USD = 4050 KHR
        private string _selectedCurrency = "USD"; // Default currency
        private decimal _amountReceived = 0;
        private decimal _change = 0;
        private bool _isKHQRMode = false;

        public PaymentForm()
        {
            InitializeComponent();
            _dbManager = new DatabaseManager();
            _unpaidBookings = new List<Booking>(); // Initialize to prevent null reference
            
            // Set form to be responsive
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void PaymentForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize form controls
                InitializeControls();
                
                // Load data
                LoadUnpaidBookings();
                
                // Set up UI
                SetupDataGrid();
                UpdateUIState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading payment form: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeControls()
        {
            try
            {
                // Make sure all UI elements are initialized
                if (cmbPaymentMethod == null || cmbCurrency == null || txtAmount == null || 
                    txtAmountReceived == null || txtChange == null || lblStatus == null || 
                    lblExchangeRate == null)
                {
                    MessageBox.Show("Error: Some UI controls are not initialized. Please restart the application.", 
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            
                // Set up payment method combo box
                cmbPaymentMethod.Items.Clear(); // Clear first to avoid duplicates
                cmbPaymentMethod.Items.AddRange(new string[] { "Cash", "KHQR Cash", "Credit Card", "Debit Card", "Bank Transfer" });
                cmbPaymentMethod.SelectedIndex = 0;
                
                // Set up currency combo box
                cmbCurrency.Items.Clear(); // Clear first to avoid duplicates
                cmbCurrency.Items.AddRange(new string[] { "USD", "KHR" });
                cmbCurrency.SelectedIndex = 0;
                
                // Initialize amount text boxes
                txtAmount.Text = "0.00";
                txtAmountReceived.Text = "0.00";
                txtChange.Text = "0.00";
                
                // Initialize status label
                lblStatus.Text = "Select a booking to process payment";
                
                // Lock amount text box as it's calculated based on booking
                txtAmount.ReadOnly = true;
                
                // Set the exchange rate label
                lblExchangeRate.Text = string.Format("Exchange Rate: 1 USD = {0} KHR", KHR_EXCHANGE_RATE);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing controls: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void SetupDataGrid()
        {
            try
            {
                // Configure DataGridView
                dgvBookings.AutoGenerateColumns = false;
                dgvBookings.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dgvBookings.MultiSelect = false;
                dgvBookings.ReadOnly = true;
                dgvBookings.AllowUserToAddRows = false;
                dgvBookings.AllowUserToDeleteRows = false;
                dgvBookings.RowHeadersVisible = false;
                dgvBookings.BackgroundColor = Color.White;
                dgvBookings.BorderStyle = BorderStyle.None;
                dgvBookings.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                
                // Define columns
                dgvBookings.Columns.Clear();
                
                DataGridViewTextBoxColumn colId = new DataGridViewTextBoxColumn();
                colId.DataPropertyName = "BookingId";
                colId.HeaderText = "ID";
                colId.Width = 50;
                dgvBookings.Columns.Add(colId);
                
                DataGridViewTextBoxColumn colGuestName = new DataGridViewTextBoxColumn();
                colGuestName.DataPropertyName = "GuestName";
                colGuestName.HeaderText = "Guest";
                colGuestName.Width = 150;
                dgvBookings.Columns.Add(colGuestName);
                
                DataGridViewTextBoxColumn colRoomNumber = new DataGridViewTextBoxColumn();
                colRoomNumber.DataPropertyName = "RoomNumber";
                colRoomNumber.HeaderText = "Room";
                colRoomNumber.Width = 80;
                dgvBookings.Columns.Add(colRoomNumber);
                
                DataGridViewTextBoxColumn colCheckIn = new DataGridViewTextBoxColumn();
                colCheckIn.DataPropertyName = "CheckInDate";
                colCheckIn.HeaderText = "Check In";
                colCheckIn.Width = 100;
                colCheckIn.DefaultCellStyle.Format = "d";
                dgvBookings.Columns.Add(colCheckIn);
                
                DataGridViewTextBoxColumn colCheckOut = new DataGridViewTextBoxColumn();
                colCheckOut.DataPropertyName = "CheckOutDate";
                colCheckOut.HeaderText = "Check Out";
                colCheckOut.Width = 100;
                colCheckOut.DefaultCellStyle.Format = "d";
                dgvBookings.Columns.Add(colCheckOut);
                
                DataGridViewTextBoxColumn colStatus = new DataGridViewTextBoxColumn();
                colStatus.DataPropertyName = "BookingStatus";
                colStatus.HeaderText = "Status";
                colStatus.Width = 100;
                dgvBookings.Columns.Add(colStatus);
                
                DataGridViewTextBoxColumn colAmount = new DataGridViewTextBoxColumn();
                colAmount.DataPropertyName = "TotalAmount";
                colAmount.HeaderText = "Amount";
                colAmount.Width = 100;
                colAmount.DefaultCellStyle.Format = "C";
                dgvBookings.Columns.Add(colAmount);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error setting up data grid: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadUnpaidBookings()
        {
            try
            {
                // Check if database manager is null - but don't assign to readonly field
                // Initialize _unpaidBookings safely without trying to modify the readonly field
                var dbManager = _dbManager ?? new DatabaseManager();
                
                // Safely get unpaid bookings, handling potential nulls
                _unpaidBookings = dbManager.GetUnpaidBookings();
                
                if (_unpaidBookings == null)
                {
                    _unpaidBookings = new List<Booking>();
                }
                
                // Make sure dgvBookings is initialized
                if (dgvBookings == null)
                {
                    MessageBox.Show("Error: DataGridView is not initialized.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                // Update the UI
                ApplyFilters();
                
                // Check if we should preselect a booking based on SelectedBookingId setting
                int selectedBookingId = Properties.Settings.Default.SelectedBookingId;
                
                if (selectedBookingId > 0 && dgvBookings.Rows.Count > 0)
                {
                    // Find the booking in the data source
                    foreach (DataGridViewRow row in dgvBookings.Rows)
                    {
                        if (row.DataBoundItem is Booking booking && booking.BookingId == selectedBookingId)
                        {
                            // Select this row
                            row.Selected = true;
                            dgvBookings.FirstDisplayedScrollingRowIndex = row.Index;
                            
                            // Reset the setting to 0 so it doesn't affect future form loads
                            Properties.Settings.Default.SelectedBookingId = 0;
                            Properties.Settings.Default.Save();
                            
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading unpaid bookings: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                // Initialize with empty list to prevent null reference
                _unpaidBookings = new List<Booking>();
                if (dgvBookings != null)
                {
                    dgvBookings.DataSource = null;
                }
                
                // Update the label if it exists
                if (lblBookingsCount != null)
                {
                    lblBookingsCount.Text = "Unpaid Bookings: 0";
                }
            }
        }

        private void ApplyFilters()
        {
            try
            {
                // Check if our list and UI elements are initialized
                if (_unpaidBookings == null)
                {
                    _unpaidBookings = new List<Booking>();
                }
                
                if (dgvBookings == null || txtSearch == null || lblBookingsCount == null)
                {
                    MessageBox.Show("Error: Some UI controls are not initialized.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                var filteredBookings = _unpaidBookings;
                
                // Apply search filter
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    string searchTerm = txtSearch.Text.ToLower();
                    filteredBookings = filteredBookings.Where(b => 
                        (b.GuestName?.ToLower()?.Contains(searchTerm) ?? false) ||
                        (b.RoomNumber?.ToLower()?.Contains(searchTerm) ?? false) ||
                        b.BookingId.ToString().Contains(searchTerm)
                    ).ToList();
                }
                
                // Update bookings count
                lblBookingsCount.Text = string.Format("Unpaid Bookings: {0}", filteredBookings.Count);
                
                // Update the data source
                dgvBookings.DataSource = null;
                dgvBookings.DataSource = filteredBookings;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error applying filters: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                // Clear data source to prevent further errors
                if (dgvBookings != null)
                {
                    dgvBookings.DataSource = null;
                }
                
                // Update the label if it exists
                if (lblBookingsCount != null)
                {
                    lblBookingsCount.Text = "Unpaid Bookings: 0";
                }
            }
        }

        private void UpdateUIState()
        {
            try
            {
                // For KHQR mode, enable process button if booking is selected
                if (_isKHQRMode)
                {
                    btnProcess.Enabled = _selectedBooking != null && cmbPaymentMethod.SelectedIndex >= 0;
                    btnProcess.Text = "Start KHQR Payment";

                    // Hide amount received and change fields for KHQR
                    txtAmountReceived.Visible = false;
                    lblAmountReceived.Visible = false;
                    txtChange.Visible = false;
                    lblChange.Visible = false;
                }
                else
                {
                    // Normal payment mode
                    btnProcess.Text = "Process";

                    // Show amount received and change fields
                    txtAmountReceived.Visible = true;
                    lblAmountReceived.Visible = true;
                    txtChange.Visible = true;
                    lblChange.Visible = true;

                    // Enable/disable process button based on booking selection and valid payment amounts
                    decimal tempAmountReceived = 0;
                    decimal tempAmount = 0;
                    btnProcess.Enabled = _selectedBooking != null &&
                                         decimal.TryParse(txtAmount.Text, out tempAmount) &&
                                         decimal.TryParse(txtAmountReceived.Text, out tempAmountReceived) &&
                                         tempAmountReceived > 0 &&
                                         cmbPaymentMethod.SelectedIndex >= 0;
                }
                
                // Update status label and amount due
                if (_selectedBooking != null)
                {
                    // Calculate amount due
                    _amountDue = _selectedBooking.TotalAmount;
                    if (_amountDue <= 0)
                    {
                        // Calculate based on room rate and stay duration
                        Room room = _dbManager.GetRoomById(_selectedBooking.RoomId);
                        if (room != null)
                        {
                            int nights = _selectedBooking.GetNumberOfNights();
                            _amountDue = room.RatePerNight * nights;
                        }
                    }
                    
                    // Update UI with correct currency format
                    UpdateCurrencyDisplay();
                    
                    // Show booking details
                    lblBookingDetails.Text = string.Format("Check-in: {0:d}, Check-out: {1:d}, Nights: {2}, Adults: {3}, Children: {4}",
                        _selectedBooking.CheckInDate, _selectedBooking.CheckOutDate, 
                        _selectedBooking.GetNumberOfNights(), _selectedBooking.NumberOfAdults,
                        _selectedBooking.NumberOfChildren);
                }
                else
                {
                    _amountDue = 0;
                    txtAmount.Text = "0.00";
                    lblStatus.Text = "Select a booking to process payment";
                    lblBookingDetails.Text = "";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating UI state: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void UpdateCurrencyDisplay()
        {
            try
            {
                if (_selectedCurrency == "USD")
                {
                    // Display in USD
                    txtAmount.Text = _amountDue.ToString("F2");
                    lblStatus.Text = string.Format("Processing payment for {0}, Room {1}, Amount Due: ${2:F2} USD", 
                        _selectedBooking?.GuestName ?? "Unknown", _selectedBooking?.RoomNumber ?? "Unknown", _amountDue);
                        
                    // Calculate change in USD
                    CalculateChange();
                }
                else // KHR
                {
                    // Convert to KHR and display
                    decimal amountInKHR = _amountDue * KHR_EXCHANGE_RATE;
                    txtAmount.Text = amountInKHR.ToString("F0");
                    lblStatus.Text = string.Format("Processing payment for {0}, Room {1}, Amount Due: {2:N0} KHR", 
                        _selectedBooking?.GuestName ?? "Unknown", _selectedBooking?.RoomNumber ?? "Unknown", amountInKHR);
                        
                    // Calculate change in KHR
                    CalculateChange();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating currency display: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void CalculateChange()
        {
            try
            {
                decimal amountReceived = 0;
                if (decimal.TryParse(txtAmountReceived.Text, out amountReceived))
                {
                    if (_selectedCurrency == "USD")
                    {
                        // Calculate change in USD
                        _change = amountReceived - _amountDue;
                        
                        // Display with USD formatting
                        txtChange.Text = _change > 0 ? _change.ToString("F2") : "0.00";
                    }
                    else // KHR
                    {
                        // Calculate change in KHR
                        decimal amountDueInKHR = _amountDue * KHR_EXCHANGE_RATE;
                        _change = amountReceived - amountDueInKHR;
                        
                        // Display with KHR formatting (no decimal places)
                        txtChange.Text = _change > 0 ? _change.ToString("F0") : "0";
                    }
                }
                else
                {
                    txtChange.Text = "0.00";
                }
            }
            catch (Exception ex)
            {
                txtChange.Text = "0.00";
            }
        }

        // Event Handlers
        private void btnSearch_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }
        
        private void txtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                ApplyFilters();
            }
        }
        
        private void txtAmountReceived_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                // Allow only digits, decimal point, and control characters
                if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
                {
                    e.Handled = true;
                }
                
                // For KHR currency, don't allow decimal points
                if (_selectedCurrency == "KHR" && e.KeyChar == '.')
                {
                    e.Handled = true;
                }
                
                // Allow only one decimal point for USD
                if (_selectedCurrency == "USD" && e.KeyChar == '.' && txtAmountReceived.Text.Contains('.'))
                {
                    e.Handled = true;
                }
            }
            catch
            {
                e.Handled = true;
            }
        }
        
        private void txtAmountReceived_TextChanged(object sender, EventArgs e)
        {
            CalculateChange();
            UpdateUIState();
        }
        
        private void cmbPaymentMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                string selectedMethod = cmbPaymentMethod.SelectedItem?.ToString() ?? "";

                if (selectedMethod == "KHQR Cash")
                {
                    // Switch to KHQR mode
                    _isKHQRMode = true;
                    ShowKHQRInterface();
                }
                else
                {
                    // Switch back to normal payment mode
                    _isKHQRMode = false;
                    ShowNormalInterface();
                }

                UpdateUIState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error changing payment method: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void cmbCurrency_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cmbCurrency.SelectedItem != null)
                {
                    _selectedCurrency = cmbCurrency.SelectedItem.ToString();
                    
                    // Clear amount received and change when currency changes
                    txtAmountReceived.Text = "0.00";
                    txtChange.Text = "0.00";
                    
                    UpdateCurrencyDisplay();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error changing currency: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                // Default back to USD if there's an error
                _selectedCurrency = "USD";
                if (cmbCurrency.Items.Count > 0)
                {
                    cmbCurrency.SelectedIndex = 0;
                }
            }
        }
        
        private void dgvBookings_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgvBookings.SelectedRows.Count > 0 && dgvBookings.SelectedRows[0].DataBoundItem is Booking booking)
                {
                    _selectedBooking = booking;
                }
                else
                {
                    _selectedBooking = null;
                }
                
                // Reset amount received and change when booking changes
                txtAmountReceived.Text = "0.00";
                txtChange.Text = "0.00";
                
                UpdateUIState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error selecting booking: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _selectedBooking = null;
                UpdateUIState();
            }
        }
        
        private void btnProcess_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedBooking == null)
                {
                    MessageBox.Show("Please select a booking to process payment.", "Selection Required",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Handle KHQR payments differently
                if (_isKHQRMode)
                {
                    ProcessKHQRPayment();
                    return;
                }
                
                // Validate amount received
                decimal amountReceived = 0;
                if (!decimal.TryParse(txtAmountReceived.Text, out amountReceived) || amountReceived <= 0)
                {
                    MessageBox.Show("Please enter a valid payment amount.", "Invalid Amount", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                // Validate change (should not be negative)
                decimal change = 0;
                if (!decimal.TryParse(txtChange.Text, out change) || change < 0)
                {
                    MessageBox.Show("The amount received is less than the amount due.", "Insufficient Payment", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                decimal amountToProcess = _amountDue; // Always store the USD amount in database
                string paymentMethod = cmbPaymentMethod.SelectedItem?.ToString() ?? "Cash";
                string currencyUsed = _selectedCurrency;
                
                // Prepare confirmation message with currency information
                string confirmMessage;
                
                if (_selectedCurrency == "USD")
                {
                    confirmMessage = string.Format(
                        "Process payment of ${0:F2} USD for {1}, Room {2}?\n\n" +
                        "Amount Received: ${3:F2} USD\n" +
                        "Change: ${4:F2} USD", 
                        _amountDue, _selectedBooking.GuestName, _selectedBooking.RoomNumber,
                        amountReceived, change);
                }
                else // KHR
                {
                    decimal amountDueInKHR = _amountDue * KHR_EXCHANGE_RATE;
                    confirmMessage = string.Format(
                        "Process payment of {0:N0} KHR for {1}, Room {2}?\n\n" +
                        "Amount Received: {3:N0} KHR\n" +
                        "Change: {4:N0} KHR", 
                        amountDueInKHR, _selectedBooking.GuestName, _selectedBooking.RoomNumber,
                        amountReceived, change);
                }
                
                // Confirm payment
                DialogResult result = MessageBox.Show(
                    confirmMessage,
                    "Confirm Payment", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                if (result == DialogResult.Yes)
                {
                    // Add currency information to payment method if KHR was used
                    string paymentDetails = paymentMethod;
                    if (_selectedCurrency == "KHR")
                    {
                        paymentDetails += " (KHR)";
                    }
                    
                    // Process payment
                    bool success = _dbManager.ProcessPaymentWithMethod(_selectedBooking.BookingId, amountToProcess, paymentDetails);
                    
                    if (success)
                    {
                        string successMessage;
                        if (_selectedCurrency == "USD")
                        {
                            successMessage = string.Format("Payment of ${0:F2} USD successfully processed for booking #{1}.\n\n" +
                                "Amount Received: ${2:F2} USD\nChange: ${3:F2} USD", 
                                _amountDue, _selectedBooking.BookingId, amountReceived, change);
                        }
                        else // KHR
                        {
                            decimal amountDueInKHR = _amountDue * KHR_EXCHANGE_RATE;
                            successMessage = string.Format("Payment of {0:N0} KHR successfully processed for booking #{1}.\n\n" +
                                "Amount Received: {2:N0} KHR\nChange: {3:N0} KHR", 
                                amountDueInKHR, _selectedBooking.BookingId, amountReceived, change);
                        }
                        
                        MessageBox.Show(successMessage, "Payment Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                        // Refresh data
                        LoadUnpaidBookings();
                        
                        // Clear selection and amounts
                        _selectedBooking = null;
                        txtAmountReceived.Text = "0.00";
                        txtChange.Text = "0.00";
                        UpdateUIState();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("Error processing payment: {0}", ex.Message), "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void btnCancel_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void ShowKHQRInterface()
        {
            try
            {
                if (_selectedBooking == null)
                {
                    MessageBox.Show("Please select a booking first.", "Selection Required",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Hide the normal payment interface
                dgvBookings.Visible = false;
                lblBookingsCount.Visible = false;
                lblBookingDetails.Visible = false;

                // Show the WebBrowser for KHQR
                webBrowserKHQR.Visible = true;

                // Generate KHQR URL with booking details
                string khqrUrl = GenerateKHQRUrl();

                // Navigate to the KHQR payment page
                webBrowserKHQR.Navigate(khqrUrl);

                // Update status
                lblStatus.Text = $"KHQR Payment for {_selectedBooking.GuestName}, Room {_selectedBooking.RoomNumber}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error showing KHQR interface: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowNormalInterface()
        {
            try
            {
                // Show the normal payment interface
                dgvBookings.Visible = true;
                lblBookingsCount.Visible = true;
                lblBookingDetails.Visible = true;

                // Hide the WebBrowser
                webBrowserKHQR.Visible = false;

                // Update status
                UpdateUIState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error showing normal interface: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateKHQRUrl()
        {
            try
            {
                // Base URL for your KHQR system
                string baseUrl = "http://localhost/hotel/request.php";

                // Parameters for KHQR payment
                var parameters = new List<string>
                {
                    $"amount={_amountDue}",
                    $"username={Uri.EscapeDataString(_selectedBooking.GuestName ?? "")}",
                    $"booking_id={_selectedBooking.BookingId}",
                    $"company_name={Uri.EscapeDataString("Hotel Management System")}",
                    $"profile_id={Uri.EscapeDataString("hotel_" + _selectedBooking.BookingId)}"
                };

                return $"{baseUrl}?{string.Join("&", parameters)}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating KHQR URL: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return "about:blank";
            }
        }

        private void ProcessKHQRPayment()
        {
            try
            {
                // For KHQR payments, we just show a message that payment is being processed
                // The actual payment verification will happen through the PHP system
                MessageBox.Show(
                    $"KHQR payment initiated for {_selectedBooking.GuestName}, Room {_selectedBooking.RoomNumber}.\n\n" +
                    $"Amount: ${_amountDue:F2} USD\n\n" +
                    "Please scan the QR code with your banking app to complete the payment.\n" +
                    "The system will automatically detect when payment is received.",
                    "KHQR Payment Initiated",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // Update the booking status to indicate KHQR payment is pending
                // You might want to add a new status like "KHQR_PENDING" to your database

                // Start monitoring payment status in background
                Task.Run(() => StartPaymentStatusMonitoring());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing KHQR payment: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task<bool> CheckPaymentStatusAsync(int bookingId)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    string url = $"http://localhost/hotel/check_payment_status.php?booking_id={bookingId}";
                    HttpResponseMessage response = await client.GetAsync(url);

                    if (response.IsSuccessStatusCode)
                    {
                        string jsonResponse = await response.Content.ReadAsStringAsync();

                        // Simple JSON parsing without external libraries
                        if (jsonResponse.Contains("\"success\":true") &&
                            jsonResponse.Contains("\"payment_status\":\"completed\""))
                        {
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't show to user as this runs in background
                System.Diagnostics.Debug.WriteLine($"Error checking payment status: {ex.Message}");
            }

            return false;
        }

        private async void StartPaymentStatusMonitoring()
        {
            if (_selectedBooking == null) return;

            try
            {
                // Check payment status every 10 seconds for up to 5 minutes
                int maxAttempts = 30; // 30 * 10 seconds = 5 minutes
                int attempts = 0;

                while (attempts < maxAttempts && _isKHQRMode)
                {
                    await Task.Delay(10000); // Wait 10 seconds

                    bool paymentCompleted = await CheckPaymentStatusAsync(_selectedBooking.BookingId);

                    if (paymentCompleted)
                    {
                        // Payment completed - update UI on main thread
                        this.Invoke(new Action(() =>
                        {
                            MessageBox.Show(
                                $"KHQR payment completed successfully!\n\n" +
                                $"Booking: {_selectedBooking.BookingId}\n" +
                                $"Guest: {_selectedBooking.GuestName}\n" +
                                $"Amount: ${_amountDue:F2} USD",
                                "Payment Completed",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);

                            // Process the payment in the local database
                            bool success = _dbManager.ProcessPaymentWithMethod(_selectedBooking.BookingId, _amountDue, "KHQR Cash");

                            if (success)
                            {
                                // Refresh data and return to normal mode
                                LoadUnpaidBookings();
                                _isKHQRMode = false;
                                ShowNormalInterface();
                                cmbPaymentMethod.SelectedIndex = 0; // Reset to Cash
                            }
                        }));

                        return; // Exit monitoring
                    }

                    attempts++;
                }

                // Timeout reached
                if (_isKHQRMode)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show(
                            "Payment monitoring timeout reached.\n\n" +
                            "Please check the payment status manually or try again.",
                            "Monitoring Timeout",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);
                    }));
                }
            }
            catch (Exception ex)
            {
                this.Invoke(new Action(() =>
                {
                    MessageBox.Show($"Error monitoring payment status: {ex.Message}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }));
            }
        }

        private void txtAmount_KeyPress(object sender, KeyPressEventArgs e)
        {
            // This is here for compatibility with the designer code
            // The actual implementation is in txtAmountReceived_KeyPress
        }
        
        private void txtAmount_TextChanged(object sender, EventArgs e)
        {
            // This is here for compatibility with the designer code
            // The actual implementation is in txtAmountReceived_TextChanged
        }
    }
} 