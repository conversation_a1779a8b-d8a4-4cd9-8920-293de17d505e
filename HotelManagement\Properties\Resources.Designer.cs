namespace HotelManagement.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("HotelManagement.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        internal static string About {
            get {
                return ResourceManager.GetString("About", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Types.
        /// </summary>
        internal static string AllTypes {
            get {
                return ResourceManager.GetString("AllTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hotel Management System.
        /// </summary>
        internal static string AppTitle {
            get {
                return ResourceManager.GetString("AppTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available.
        /// </summary>
        internal static string Available {
            get {
                return ResourceManager.GetString("Available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Only.
        /// </summary>
        internal static string AvailableOnly {
            get {
                return ResourceManager.GetString("AvailableOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bookings.
        /// </summary>
        internal static string Bookings {
            get {
                return ResourceManager.GetString("Bookings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        internal static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation.
        /// </summary>
        internal static string Confirmation {
            get {
                return ResourceManager.GetString("Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this item?.
        /// </summary>
        internal static string ConfirmDelete {
            get {
                return ResourceManager.GetString("ConfirmDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database connection failed.
        /// </summary>
        internal static string ConnectionFailed {
            get {
                return ResourceManager.GetString("ConnectionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboard.
        /// </summary>
        internal static string Dashboard {
            get {
                return ResourceManager.GetString("Dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deluxe.
        /// </summary>
        internal static string Deluxe {
            get {
                return ResourceManager.GetString("Deluxe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double.
        /// </summary>
        internal static string Double {
            get {
                return ResourceManager.GetString("Double", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate record.
        /// </summary>
        internal static string DuplicateRecord {
            get {
                return ResourceManager.GetString("DuplicateRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        internal static string Exit {
            get {
                return ResourceManager.GetString("Exit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        internal static string ExitButton {
            get {
                return ResourceManager.GetString("ExitButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        internal static string Filter {
            get {
                return ResourceManager.GetString("Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guest Management.
        /// </summary>
        internal static string GuestManagement {
            get {
                return ResourceManager.GetString("GuestManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Help.
        /// </summary>
        internal static string Help {
            get {
                return ResourceManager.GetString("Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid credentials.
        /// </summary>
        internal static string InvalidCredentials {
            get {
                return ResourceManager.GetString("InvalidCredentials", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid format.
        /// </summary>
        internal static string InvalidFormat {
            get {
                return ResourceManager.GetString("InvalidFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string LoginButton {
            get {
                return ResourceManager.GetString("LoginButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string LoginFormTitle {
            get {
                return ResourceManager.GetString("LoginFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout.
        /// </summary>
        internal static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        internal static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payments.
        /// </summary>
        internal static string Payments {
            get {
                return ResourceManager.GetString("Payments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        internal static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record deleted successfully.
        /// </summary>
        internal static string RecordDeleted {
            get {
                return ResourceManager.GetString("RecordDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record not found.
        /// </summary>
        internal static string RecordNotFound {
            get {
                return ResourceManager.GetString("RecordNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record saved successfully.
        /// </summary>
        internal static string RecordSaved {
            get {
                return ResourceManager.GetString("RecordSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record updated successfully.
        /// </summary>
        internal static string RecordUpdated {
            get {
                return ResourceManager.GetString("RecordUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This field is required.
        /// </summary>
        internal static string RequiredField {
            get {
                return ResourceManager.GetString("RequiredField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Room Details.
        /// </summary>
        internal static string RoomDetails {
            get {
                return ResourceManager.GetString("RoomDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Room List.
        /// </summary>
        internal static string RoomList {
            get {
                return ResourceManager.GetString("RoomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Room Management.
        /// </summary>
        internal static string RoomManagement {
            get {
                return ResourceManager.GetString("RoomManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Room Number.
        /// </summary>
        internal static string RoomNumber {
            get {
                return ResourceManager.GetString("RoomNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Room Type.
        /// </summary>
        internal static string RoomType {
            get {
                return ResourceManager.GetString("RoomType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        internal static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        internal static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Single.
        /// </summary>
        internal static string Single {
            get {
                return ResourceManager.GetString("Single", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Staff Management.
        /// </summary>
        internal static string StaffManagement {
            get {
                return ResourceManager.GetString("StaffManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success.
        /// </summary>
        internal static string Success {
            get {
                return ResourceManager.GetString("Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suite.
        /// </summary>
        internal static string Suite {
            get {
                return ResourceManager.GetString("Suite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        internal static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        internal static string Username {
            get {
                return ResourceManager.GetString("Username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        internal static string Warning {
            get {
                return ResourceManager.GetString("Warning", resourceCulture);
            }
        }
    }
} 