<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Awesome Incremented and Contributors</dc:creator>
  <dc:description>A library for embbeding Font Awesome icons in WPF &amp; Windows Forms applications</dc:description>
  <dc:identifier>FontAwesome.Sharp</dc:identifier>
  <version>5.15.4</version>
  <keywords>fontawesome icon material-design dotnet-core wpf windows-forms</keywords>
  <lastModifiedBy>NuGet.Build.Tasks.Pack, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35;</lastModifiedBy>
</coreProperties>