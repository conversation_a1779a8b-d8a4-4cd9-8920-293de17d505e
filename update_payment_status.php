<?php
// Update payment status when KHQR payment is completed
// This is called by qrcode.php when payment is detected via NBC Bakong API

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

try {
    // Get POST parameters
    $profileId = isset($_POST['profile_id']) ? $_POST['profile_id'] : '';
    $md5 = isset($_POST['md5']) ? $_POST['md5'] : '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $username = isset($_POST['username']) ? $_POST['username'] : ''; // This is room number
    $roomId = isset($_POST['room_id']) ? intval($_POST['room_id']) : 0;
    $bookingId = isset($_POST['booking_id']) ? intval($_POST['booking_id']) : 0;
    $guestName = isset($_POST['guest_name']) ? $_POST['guest_name'] : '';
    $payerAccount = isset($_POST['payer']) ? $_POST['payer'] : '';
    $shortHash = isset($_POST['short_hash']) ? $_POST['short_hash'] : '';
    
    error_log("UPDATE_PAYMENT_STATUS.PHP - Received: room_id=$roomId, booking_id=$bookingId, guest_name=$guestName, amount=$amount, payer=$payerAccount");
    
    if (empty($md5)) {
        throw new Exception('MD5 hash is required');
    }
    
    // Update qrcode_data status to completed
    $updateQrQuery = "UPDATE qrcode_data SET status = 'completed' WHERE md5 = ?";
    if (is_resource($conn)) {
        // SQL Server
        $stmt = sqlsrv_query($conn, $updateQrQuery, [$md5]);
        $success = ($stmt !== false);
    } else {
        // MySQL
        $stmt = $conn->prepare($updateQrQuery);
        $stmt->bind_param("s", $md5);
        $success = $stmt->execute();
        $stmt->close();
    }
    
    if (!$success) {
        throw new Exception('Failed to update QR code status');
    }
    
    // Update payments table if it exists
    if (!empty($profileId)) {
        // Get user_id from profile_id
        if (is_resource($conn)) {
            // SQL Server - not implemented for this demo
            error_log("SQL Server payment update not implemented");
        } else {
            // MySQL
            $userQuery = "SELECT id FROM users WHERE profile_id = ?";
            $userStmt = $conn->prepare($userQuery);
            $userStmt->bind_param("s", $profileId);
            $userStmt->execute();
            $userResult = $userStmt->get_result();
            
            if ($userRow = $userResult->fetch_assoc()) {
                $userId = $userRow['id'];
                
                // Update payment status to completed
                $updatePaymentQuery = "UPDATE payments SET status = 'completed', payer_account = ?, short_hash = ? WHERE user_id = ? AND md5 = ?";
                $paymentStmt = $conn->prepare($updatePaymentQuery);
                $paymentStmt->bind_param("ssis", $payerAccount, $shortHash, $userId, $md5);
                $paymentStmt->execute();
                $paymentStmt->close();
                
                error_log("Updated payment status for user_id=$userId, md5=$md5");
            }
            $userStmt->close();
        }
    }
    
    // If we have booking information, we could also update the hotel's booking system
    // This would require integration with the C# application's database
    if ($bookingId > 0) {
        // For now, just log this information
        error_log("KHQR Payment completed for Booking ID: $bookingId, Room ID: $roomId, Guest: $guestName, Amount: $amount, Payer: $payerAccount");
        
        // In a real implementation, you might want to:
        // 1. Update the hotel's Bookings table to mark payment as received
        // 2. Send a notification to the hotel management system
        // 3. Generate a receipt or confirmation
        
        // For now, we'll create a simple log entry that the C# application can check
        if (!is_resource($conn)) {
            // MySQL - create a simple notification table entry
            $notificationQuery = "INSERT INTO hotel_payment_notifications (booking_id, room_id, guest_name, amount, payer_account, short_hash, md5, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE payer_account = VALUES(payer_account), short_hash = VALUES(short_hash), created_at = NOW()";
            
            // Check if table exists, if not create it
            $createTableQuery = "CREATE TABLE IF NOT EXISTS hotel_payment_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                booking_id INT,
                room_id INT,
                guest_name VARCHAR(100),
                amount DECIMAL(10,2),
                payer_account VARCHAR(100),
                short_hash VARCHAR(8),
                md5 VARCHAR(32),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed BOOLEAN DEFAULT FALSE,
                UNIQUE KEY unique_booking (booking_id, md5)
            )";
            $conn->query($createTableQuery);
            
            $notificationStmt = $conn->prepare($notificationQuery);
            $notificationStmt->bind_param("iisdsss", $bookingId, $roomId, $guestName, $amount, $payerAccount, $shortHash, $md5);
            $notificationStmt->execute();
            $notificationStmt->close();
        }
    }
    
    $response['success'] = true;
    $response['message'] = 'Payment status updated successfully';
    $response['data'] = [
        'booking_id' => $bookingId,
        'room_id' => $roomId,
        'guest_name' => $guestName,
        'amount' => $amount,
        'payer_account' => $payerAccount,
        'short_hash' => $shortHash,
        'md5' => $md5
    ];
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    error_log("Payment status update error: " . $e->getMessage());
}

// Close database connection
if (is_resource($conn)) {
    sqlsrv_close($conn);
} else {
    $conn->close();
}

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
