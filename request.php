<?php
// Set CORS headers to allow iframe embedding from any domain
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type");

// Use proper CSP with frame-ancestors
header("Content-Security-Policy: frame-ancestors *");

// Add Cross-Origin-Resource-Policy header
header("Cross-Origin-Resource-Policy: cross-origin");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Payment</title>
    <script src="khqr-1.0.6.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            background-color: #f5f5f5;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 5px solid rgba(13, 159, 225, 0.2);
            border-top: 5px solid #0d9fe1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .loading-subtext {
            font-size: 14px;
            color: #666;
        }
        
        #response {
            display: none; /* Hide the response div initially */
        }
    </style>
</head>
<body>
    <!-- Full-page loading overlay that shows immediately -->
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing Payment</div>
        <div class="loading-subtext">Please wait while we generate your QR code...</div>
    </div>
    
    <!-- Hidden response div -->
    <div id="response"></div>

    <script>
        // Define utility functions first
        function getParameterByName(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, "\\$&");
            const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return "";
            return decodeURIComponent(results[2].replace(/\+/g, " "));
        }

        function getAmountFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return parseFloat(urlParams.get("amount"));
        }

        function getUsernameFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get("username") || "";
        }

        function generateBillNumber() {
            const currentDate = new Date();
            const formattedDate = currentDate.toISOString().replace(/[-:.TZ]/g, "");
            const day = formattedDate.substr(0, 8);
            const randomNumber = Math.floor(Math.random() * 1000);
            return `KHQR${day}${randomNumber}`;
        }

        document.addEventListener("DOMContentLoaded", function () {
            try {
                // Make sure the library is loaded
                if (!window.BakongKHQR) {
                    throw new Error("BakongKHQR library not loaded. Please check your internet connection.");
                }

                const { BakongKHQR, khqrData, IndividualInfo } = window.BakongKHQR;

                // Get parameters from URL
                const profileId = getParameterByName("profile_id") || 'hotel_profile';
                const urlCompanyName = getParameterByName("company_name");
                const urlBakongId = getParameterByName("bakong_id");
                const amount = getAmountFromUrl() || 1;
                const username = getUsernameFromUrl();

                // Use values from URL or defaults
                const bakongId = urlBakongId || "leapmeng_hai1@trmc";
                const companyName = urlCompanyName || "Hotel Management System";

                console.log("KHQR Parameters:", {
                    profileId: profileId,
                    bakongId: bakongId,
                    companyName: companyName,
                    amount: amount,
                    username: username
                });

                // Create optional data for KHQR
                const optionalData = {
                    currency: khqrData.currency.usd,
                    amount: amount,
                    mobileNumber: "85513335219",
                    storeLabel: companyName,
                    terminalLabel: companyName.substring(0, 10),
                    purposeOfTransaction: "Payment",
                    languagePreference: "km",
                    merchantNameAlternateLanguage: "ឆានអេសអឹមអឹម",
                    merchantCityAlternateLanguage: "ភ្នំពេញ",
                    expirationTimestamp: Date.now() + (2 * 60 * 1000),
                    billNumber: generateBillNumber(),
                };

                // Create individual info
                const individualInfo = new IndividualInfo(
                    bakongId,
                    companyName,
                    "Phnom Penh",
                    optionalData
                );

                // Create KHQR instance and generate QR code
                const khqr = new BakongKHQR();
                const response = khqr.generateIndividual(individualInfo);

                console.log("KHQR Response:", response);

                // Check if QR generation was successful
                if (response && response.status && response.status.code === 0 && response.data && response.data.qr && response.data.md5) {
                    const qrData = response.data.qr;
                    const md5 = response.data.md5;

                    console.log("QR Generated Successfully:", {
                        qrData: qrData.substring(0, 50) + "...",
                        md5: md5,
                        amount: amount,
                        username: username
                    });

                    // Build redirect URL with all parameters
                    const redirectParams = new URLSearchParams({
                        qr: qrData,
                        amount: amount,
                        md5: md5,
                        short_hash: md5.substring(0, 8),
                        username: username,
                        company_name: companyName,
                        profile_id: profileId,
                        bakong_id: bakongId
                    });

                    // Add optional room parameters if available
                    const roomId = getParameterByName("room_id");
                    const bookingId = getParameterByName("booking_id");
                    const guestName = getParameterByName("guest_name");

                    if (roomId) redirectParams.append('room_id', roomId);
                    if (bookingId) redirectParams.append('booking_id', bookingId);
                    if (guestName) redirectParams.append('guest_name', guestName);

                    // Redirect to qrcode.php
                    window.location.href = `qrcode.php?${redirectParams.toString()}`;
                } else {
                    document.getElementById("response").innerHTML = `
                        <h3>Failed to generate QR code</h3>
                        <p>Please try again or contact support.</p>
                        <p>Error details: ${JSON.stringify(response)}</p>
                        <button onclick="window.location.reload()">Try Again</button>
                    `;
                    console.error("KHQR Error:", response);
                }
            } catch (error) {
                document.getElementById("response").innerHTML = `
                    <h3>Error</h3>
                    <p>${error.message}</p>
                    <button onclick="window.location.reload()">Try Again</button>
                `;
                console.error("Exception:", error);
            }
        });
    </script>
</body>
</html>