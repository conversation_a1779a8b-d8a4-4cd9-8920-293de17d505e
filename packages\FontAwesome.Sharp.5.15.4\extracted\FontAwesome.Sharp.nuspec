﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>FontAwesome.Sharp</id>
    <version>5.15.4</version>
    <authors>Awesome Incremented and Contributors</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <description>A library for embbeding Font Awesome icons in WPF &amp; Windows Forms applications</description>
    <copyright>Copyright © Awesome Incremented 2015-2020</copyright>
    <tags>fontawesome icon material-design dotnet-core wpf windows-forms</tags>
    <repository url="https://github.com/awesome-inc/FontAwesome.Sharp" />
    <dependencies>
      <group targetFramework=".NETFramework4.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETFramework4.7.2" />
      <group targetFramework=".NETFramework4.8" />
      <group targetFramework=".NETCoreApp3.1" />
      <group targetFramework="net5.0-windows7.0" />
    </dependencies>
    <frameworkReferences>
      <group targetFramework=".NETCoreApp3.1">
        <frameworkReference name="Microsoft.WindowsDesktop.App" />
      </group>
      <group targetFramework="net5.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App" />
      </group>
      <group targetFramework=".NETFramework4.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETFramework4.7.2" />
      <group targetFramework=".NETFramework4.8" />
    </frameworkReferences>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="PresentationCore" targetFramework=".NETFramework4.0, .NETFramework4.5, .NETFramework4.7.2, .NETFramework4.8" />
      <frameworkAssembly assemblyName="PresentationFramework" targetFramework=".NETFramework4.0, .NETFramework4.5, .NETFramework4.7.2, .NETFramework4.8" />
      <frameworkAssembly assemblyName="System.Windows.Forms" targetFramework=".NETFramework4.0, .NETFramework4.5, .NETFramework4.7.2, .NETFramework4.8" />
      <frameworkAssembly assemblyName="System.Xaml" targetFramework=".NETFramework4.0, .NETFramework4.5, .NETFramework4.7.2, .NETFramework4.8" />
      <frameworkAssembly assemblyName="WindowsBase" targetFramework=".NETFramework4.0, .NETFramework4.5, .NETFramework4.7.2, .NETFramework4.8" />
    </frameworkAssemblies>
  </metadata>
</package>