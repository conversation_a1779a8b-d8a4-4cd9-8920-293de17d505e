using System;

namespace HotelManagement.Models
{
    public class Guest
    {
        public int GuestId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string ContactNumber { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string IDType { get; set; } // Passport, DriverLicense, NationalID
        public string IDNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string Nationality { get; set; }
        public string VIPStatus { get; set; }
        public string Notes { get; set; }

        public string FullName 
        { 
            get { return string.Format("{0} {1}", FirstName, LastName); }
        }

        public override string ToString()
        {
            return FullName;
        }
    }
} 