<?php
// Set CORS headers to allow iframe embedding from any domain
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type");

// Use proper CSP with frame-ancestors
header("Content-Security-Policy: frame-ancestors *");

// Add Cross-Origin-Resource-Policy header
header("Cross-Origin-Resource-Policy: cross-origin");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Payment</title>
    <script src="https://github.com/davidhuotkeo/bakong-khqr/releases/download/bakong-khqr-1.0.6/khqr-1.0.6.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            background-color: #f5f5f5;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 5px solid rgba(13, 159, 225, 0.2);
            border-top: 5px solid #0d9fe1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .loading-subtext {
            font-size: 14px;
            color: #666;
        }
        
        #response {
            display: none; /* Hide the response div initially */
        }
    </style>
</head>
<body>
    <!-- Full-page loading overlay that shows immediately -->
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing Payment</div>
        <div class="loading-subtext">Please wait while we generate your QR code...</div>
    </div>
    
    <!-- Hidden response div -->
    <div id="response"></div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            try {
                // Make sure the library is loaded
                if (!window.BakongKHQR) {
                    throw new Error("BakongKHQR library not loaded. Please check your internet connection.");
                }

                const { BakongKHQR, khqrData, IndividualInfo } = window.BakongKHQR;

                // Get profile_id from URL or sessionStorage
                const profileId = getParameterByName("profile_id") || sessionStorage.getItem('profileId') || '';

                // Store profile_id in sessionStorage for future use
                if (profileId) {
                    sessionStorage.setItem('profileId', profileId);
                }

                // Get company name and bakong ID from URL if available, otherwise use defaults
                const urlCompanyName = getParameterByName("company_name");
                const urlBakongId = getParameterByName("bakong_id");

                // Use values from URL or defaults
                const bakongId = urlBakongId || "leapmeng_hai1@trmc";
                const companyName = urlCompanyName || "CHHEAN-SMM.NET";

                console.log("Bakong ID from URL:", urlBakongId);
                console.log("Company name from URL:", urlCompanyName);
                console.log("Using Bakong ID:", bakongId);
                console.log("Using company name:", companyName);

                // Store in sessionStorage for other pages
                sessionStorage.setItem('bakongId', bakongId);
                sessionStorage.setItem('companyName', companyName);

                console.log("Using Bakong configuration:", {
                    profileId: profileId,
                    bakongId: bakongId,
                    companyName: companyName
                });

                // Get amount from URL
                const amount = getAmountFromUrl() || 1;

                const optionalData = {
                    currency: khqrData.currency.usd,
                    amount: amount,
                    mobileNumber: "85513335219",
                    storeLabel: companyName,
                    terminalLabel: companyName.substring(0, 10), // Truncate if too long
                    purposeOfTransaction: "Payment",
                    languagePreference: "km",
                    merchantNameAlternateLanguage: "ឆានអេសអឹមអឹម",
                    merchantCityAlternateLanguage: "ភ្នំពេញ",
                    expirationTimestamp: Date.now() + (2 * 60 * 1000),
                    billNumber: generateBillNumber(),
                };

                console.log("Creating IndividualInfo with:", {
                    bakongId: bakongId,
                    companyName: companyName
                });

                const individualInfo = new IndividualInfo(
                    bakongId,       // Use bakongId for the Bakong wallet ID
                    companyName,    // Use companyName for the account holder name
                    "Phnom Penh",   // City
                    optionalData
                );

            function getAmountFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return parseFloat(urlParams.get("amount"));
            }

            function getUsernameFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get("username") || "";
            }

            function getParameterByName(name, url) {
                if (!url) url = window.location.href;
                name = name.replace(/[\[\]]/g, "\\$&");
                const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                    results = regex.exec(url);
                if (!results) return null;
                if (!results[2]) return "";
                return decodeURIComponent(results[2].replace(/\+/g, " "));
            }

            function generateBillNumber() {
                const currentDate = new Date();
                const formattedDate = currentDate.toISOString().replace(/[-:.TZ]/g, "");
                const day = formattedDate.substr(0, 8);
                const randomNumber = Math.floor(Math.random() * 1000);
                return `KHQR${day}${randomNumber}`;
            }

            // Create KHQR instance
            const khqr = new BakongKHQR();

            // Generate QR code
            const response = khqr.generateIndividual(individualInfo);

            const responseElement = document.getElementById("response");

            // Check if QR generation was successful
            if (
                response &&
                response.status &&
                response.status.code === 0 &&
                response.data &&
                response.data.qr &&
                response.data.md5
            ) {
                const qrData = response.data.qr;
                const md5 = response.data.md5;
                const username = getUsernameFromUrl();

                console.log("QR Generated Successfully:", {
                    qrData: qrData.substring(0, 20) + "...",
                    md5: md5,
                    amount: optionalData.amount,
                    username: username,
                    profileId: profileId
                });

                // Redirect to save.php with the necessary data
                window.location.href = `save.php?qr=${encodeURIComponent(qrData)}&amount=${optionalData.amount}&md5=${md5}&username=${encodeURIComponent(username)}&company_name=${encodeURIComponent(companyName)}&profile_id=${encodeURIComponent(profileId)}&bakong_id=${encodeURIComponent(bakongId)}`;
            } else {
                responseElement.innerHTML = `
                    <h3>Failed to generate QR code</h3>
                    <p>Please try again or contact support.</p>
                    <p>Error details: ${JSON.stringify(response)}</p>
                    <button onclick="window.location.reload()">Try Again</button>
                `;
                console.error("KHQR Error:", response);
            }
            } catch (error) {
                document.getElementById("response").innerHTML = `
                    <h3>Error</h3>
                    <p>${error.message}</p>
                    <button onclick="window.location.reload()">Try Again</button>
                `;
                console.error("Exception:", error);
            }
        });
    </script>
</body>
</html>