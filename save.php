<?php
// Set timezone to Cambodia time
date_default_timezone_set('Asia/Phnom_Penh');

// Include database configuration
require_once 'db_config.php';

// Set CORS headers to allow iframe embedding from any domain
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type");

// Use proper CSP with frame-ancestors
header("Content-Security-Policy: frame-ancestors *");

// Add Cross-Origin-Resource-Policy header
header("Cross-Origin-Resource-Policy: cross-origin");

// Get the parameters from the URL
$qrData = isset($_GET['qr']) ? $_GET['qr'] : '';
$amount = isset($_GET['amount']) ? $_GET['amount'] : '';
$md5 = isset($_GET['md5']) ? $_GET['md5'] : '';
$username = isset($_GET['username']) ? $_GET['username'] : ''; // This will be room number
$roomId = isset($_GET['room_id']) && $_GET['room_id'] !== '' ? intval($_GET['room_id']) : null;
$bookingId = isset($_GET['booking_id']) && $_GET['booking_id'] !== '' ? intval($_GET['booking_id']) : null;
$guestName = isset($_GET['guest_name']) && $_GET['guest_name'] !== '' ? $_GET['guest_name'] : null;
$companyName = isset($_GET['company_name']) ? $_GET['company_name'] : '';
$profileId = isset($_GET['profile_id']) ? $_GET['profile_id'] : '';
$bakongId = isset($_GET['bakong_id']) ? $_GET['bakong_id'] : '';

// Log the received parameters for debugging
error_log("SAVE.PHP - Received: amount=$amount, room_id=$roomId, booking_id=$bookingId, guest_name=$guestName, username=$username");

// If profile_id is provided, fetch Bakong details from database
if (!empty($profileId)) {
    // Query to get Bakong wallet information
    $sql = "SELECT bakong_id, bakong_name, company_name FROM users WHERE profile_id = ?";
    $stmt = $conn->prepare($sql);

    if ($stmt) {
        $stmt->bind_param("s", $profileId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $row = $result->fetch_assoc()) {
            // Use values from database if available
            if (!empty($row['company_name'])) {
                $companyName = $row['company_name'];
                error_log("SAVE.PHP - Found company_name: " . $companyName);
            }
        }
        $stmt->close();
    }
}

// Get user's IP address
function getClientIP() {
    // For development/testing - simulate a real IP address
    // Comment this out before deploying to production
    if ($_SERVER['REMOTE_ADDR'] == '::1' || $_SERVER['REMOTE_ADDR'] == '127.0.0.1') {
        // Generate a random realistic IP for testing
        $testIPs = [
            '*************',  // TEST-NET-3 block for documentation
            '*************',  // TEST-NET-2 block for documentation
            '***********',    // TEST-NET-1 block for documentation
            '************',   // Private network
            '********',       // Private network
            '2001:db8:85a3:8d3:1319:8a2e:370:7348' // IPv6 documentation block
        ];
        return $testIPs[array_rand($testIPs)];
    }

    // Normal IP detection for production
    $ipAddress = '';
    if (isset($_SERVER['HTTP_CLIENT_IP']))
        $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
    else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_X_FORWARDED']))
        $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
    else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
        $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_FORWARDED']))
        $ipAddress = $_SERVER['HTTP_FORWARDED'];
    else if(isset($_SERVER['REMOTE_ADDR']))
        $ipAddress = $_SERVER['REMOTE_ADDR'];
    else
        $ipAddress = 'UNKNOWN';
    return $ipAddress;
}

$userIpAddress = getClientIP();

// Remove the automatic username generation
if ($username === 'null') {
    $username = '';
}

// Generate a short hash (first 8 characters of md5)
$shortHash = substr($md5, 0, 8);

// Get current time for the database
$currentTime = date('Y-m-d H:i:s');

// Function to check if a column exists in a table
function columnExists($conn, $table, $column) {
    $query = "SHOW COLUMNS FROM `$table` LIKE '$column'";
    $result = $conn->query($query);
    return ($result && $result->num_rows > 0);
}

// Check if qrcode_data table has profile_id column
if (!columnExists($conn, 'qrcode_data', 'profile_id')) {
    // Add profile_id column if it doesn't exist
    $addColumnQuery = "ALTER TABLE qrcode_data ADD COLUMN profile_id VARCHAR(50) AFTER username";
    $conn->query($addColumnQuery);
}

// Check if qrcode_data table has ip_address column
if (!columnExists($conn, 'qrcode_data', 'ip_address')) {
    // Add ip_address column if it doesn't exist
    $addColumnQuery = "ALTER TABLE qrcode_data ADD COLUMN ip_address VARCHAR(45) DEFAULT NULL COMMENT 'User IP address when transaction was created'";
    $conn->query($addColumnQuery);
}

// Check if qrcode_data table has room_id column
if (!columnExists($conn, 'qrcode_data', 'room_id')) {
    // Add room_id column if it doesn't exist
    $addColumnQuery = "ALTER TABLE qrcode_data ADD COLUMN room_id INT DEFAULT NULL COMMENT 'Hotel room ID'";
    $conn->query($addColumnQuery);
}

// Check if qrcode_data table has booking_id column
if (!columnExists($conn, 'qrcode_data', 'booking_id')) {
    // Add booking_id column if it doesn't exist
    $addColumnQuery = "ALTER TABLE qrcode_data ADD COLUMN booking_id INT DEFAULT NULL COMMENT 'Hotel booking ID'";
    $conn->query($addColumnQuery);
}

// Check if qrcode_data table has guest_name column
if (!columnExists($conn, 'qrcode_data', 'guest_name')) {
    // Add guest_name column if it doesn't exist
    $addColumnQuery = "ALTER TABLE qrcode_data ADD COLUMN guest_name VARCHAR(100) DEFAULT NULL COMMENT 'Guest name'";
    $conn->query($addColumnQuery);
}

// Check if a record with this md5 already exists
$checkMd5Query = "SELECT id FROM qrcode_data WHERE md5 = ?";
$checkMd5Stmt = $conn->prepare($checkMd5Query);
$checkMd5Stmt->bind_param("s", $md5);
$checkMd5Stmt->execute();
$checkMd5Result = $checkMd5Stmt->get_result();

if ($checkMd5Result->num_rows > 0) {
    // Record exists, update it
    $updateFields = "qr_data = ?, amount = ?, short_hash = ?, username = ?, profile_id = ?, created_at = ?";
    $updateTypes = "sdssss";
    $updateValues = [$qrData, $amount, $shortHash, $username, $profileId, $currentTime];

    if (columnExists($conn, 'qrcode_data', 'ip_address')) {
        $updateFields .= ", ip_address = ?";
        $updateTypes .= "s";
        $updateValues[] = $userIpAddress;
    }

    if (columnExists($conn, 'qrcode_data', 'room_id') && $roomId !== null) {
        $updateFields .= ", room_id = ?";
        $updateTypes .= "i";
        $updateValues[] = $roomId;
    }

    if (columnExists($conn, 'qrcode_data', 'booking_id') && $bookingId !== null) {
        $updateFields .= ", booking_id = ?";
        $updateTypes .= "i";
        $updateValues[] = $bookingId;
    }

    if (columnExists($conn, 'qrcode_data', 'guest_name') && $guestName !== null) {
        $updateFields .= ", guest_name = ?";
        $updateTypes .= "s";
        $updateValues[] = $guestName;
    }

    $updateTypes .= "s"; // for md5 WHERE clause
    $updateValues[] = $md5;

    $updateStmt = $conn->prepare("UPDATE qrcode_data SET $updateFields WHERE md5 = ?");
    $updateStmt->bind_param($updateTypes, ...$updateValues);

    if ($updateStmt->execute()) {
        // Continue with the rest of the code (payments table, redirect, etc.)
        $recordUpdated = true;
    } else {
        echo "Error updating record: " . $updateStmt->error;
        exit;
    }
    $updateStmt->close();
} else {
    // No existing record, insert a new one
    $insertFields = "qr_data, amount, md5, short_hash, username, profile_id, created_at";
    $insertPlaceholders = "?, ?, ?, ?, ?, ?, ?";
    $insertTypes = "sdsssss";
    $insertValues = [$qrData, $amount, $md5, $shortHash, $username, $profileId, $currentTime];

    if (columnExists($conn, 'qrcode_data', 'ip_address')) {
        $insertFields .= ", ip_address";
        $insertPlaceholders .= ", ?";
        $insertTypes .= "s";
        $insertValues[] = $userIpAddress;
    }

    if (columnExists($conn, 'qrcode_data', 'room_id') && $roomId !== null) {
        $insertFields .= ", room_id";
        $insertPlaceholders .= ", ?";
        $insertTypes .= "i";
        $insertValues[] = $roomId;
    }

    if (columnExists($conn, 'qrcode_data', 'booking_id') && $bookingId !== null) {
        $insertFields .= ", booking_id";
        $insertPlaceholders .= ", ?";
        $insertTypes .= "i";
        $insertValues[] = $bookingId;
    }

    if (columnExists($conn, 'qrcode_data', 'guest_name') && $guestName !== null) {
        $insertFields .= ", guest_name";
        $insertPlaceholders .= ", ?";
        $insertTypes .= "s";
        $insertValues[] = $guestName;
    }

    $stmt = $conn->prepare("INSERT INTO qrcode_data ($insertFields) VALUES ($insertPlaceholders)");
    $stmt->bind_param($insertTypes, ...$insertValues);

    if ($stmt->execute()) {
        $recordUpdated = true;
    } else {
        echo "Error inserting record: " . $stmt->error;
        exit;
    }
    $stmt->close();
}

$checkMd5Stmt->close();

// Only proceed if the record was successfully updated or inserted
if (isset($recordUpdated) && $recordUpdated) {
    // Insert into payments table for transaction history
    $transactionId = "TXN" . time() . rand(1000, 9999);
    $status = "pending"; // Initial status is pending

    // Check if payments table exists
    $checkTableQuery = "SHOW TABLES LIKE 'payments'";
    $checkTableResult = $conn->query($checkTableQuery);

    if ($checkTableResult->num_rows > 0) {
        // Get user_id based on profile_id
        $userQuery = "SELECT id FROM users WHERE profile_id = ?";
        $userStmt = $conn->prepare($userQuery);
        $userStmt->bind_param("s", $profileId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();

        if ($userResult->num_rows > 0) {
            $userData = $userResult->fetch_assoc();
            $userId = $userData['id'];

            // Insert payment record - only include columns we know exist
            $paymentStmt = $conn->prepare("INSERT INTO payments (user_id, amount, status, transaction_id) VALUES (?, ?, ?, ?)");

            $paymentStmt->bind_param("idss", $userId, $amount, $status, $transactionId);
            $paymentStmt->execute();
            $paymentStmt->close();
        }
        $userStmt->close();
    }

    // Redirect to qrcode.php with the parameters
    $redirectUrl = "qrcode.php?qr=" . urlencode($qrData) .
                   "&amount=" . urlencode($amount) .
                   "&md5=" . urlencode($md5) .
                   "&short_hash=" . urlencode($shortHash) .
                   "&username=" . urlencode($username) .
                   "&profile_id=" . urlencode($profileId);

    // Add room and booking information
    if (!empty($roomId)) {
        $redirectUrl .= "&room_id=" . urlencode($roomId);
    }

    if (!empty($bookingId)) {
        $redirectUrl .= "&booking_id=" . urlencode($bookingId);
    }

    if (!empty($guestName)) {
        $redirectUrl .= "&guest_name=" . urlencode($guestName);
    }

    // Add company name if available
    if (!empty($companyName)) {
        $redirectUrl .= "&company_name=" . urlencode($companyName);
    }

    // Add Bakong ID if available
    if (!empty($bakongId)) {
        $redirectUrl .= "&bakong_id=" . urlencode($bakongId);
    }

    header("Location: " . $redirectUrl);
    exit;
} else {
    echo "Error: Failed to update or insert record.";
}

$conn->close();
?>