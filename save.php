<?php
// Set timezone to Cambodia time
date_default_timezone_set('Asia/Phnom_Penh');

// Include database configuration
require_once 'db_config.php';

// Set CORS headers to allow iframe embedding from any domain
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type");

// Use proper CSP with frame-ancestors
header("Content-Security-Policy: frame-ancestors *");

// Add Cross-Origin-Resource-Policy header
header("Cross-Origin-Resource-Policy: cross-origin");

// Get the parameters from the URL
$qrData = isset($_GET['qr']) ? $_GET['qr'] : '';
$amount = isset($_GET['amount']) ? $_GET['amount'] : '';
$md5 = isset($_GET['md5']) ? $_GET['md5'] : '';
$username = isset($_GET['username']) ? $_GET['username'] : '';
$companyName = isset($_GET['company_name']) ? $_GET['company_name'] : '';
$profileId = isset($_GET['profile_id']) ? $_GET['profile_id'] : '';
$bakongId = isset($_GET['bakong_id']) ? $_GET['bakong_id'] : '';

// If profile_id is provided, fetch Bakong details from database
if (!empty($profileId)) {
    // Query to get Bakong wallet information
    $sql = "SELECT bakong_id, bakong_name, company_name FROM users WHERE profile_id = ?";
    $stmt = $conn->prepare($sql);

    if ($stmt) {
        $stmt->bind_param("s", $profileId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $row = $result->fetch_assoc()) {
            // Use values from database if available
            if (!empty($row['company_name'])) {
                $companyName = $row['company_name'];
                error_log("SAVE.PHP - Found company_name: " . $companyName);
            }
        }
        $stmt->close();
    }
}

// Get user's IP address
function getClientIP() {
    // For development/testing - simulate a real IP address
    // Comment this out before deploying to production
    if ($_SERVER['REMOTE_ADDR'] == '::1' || $_SERVER['REMOTE_ADDR'] == '127.0.0.1') {
        // Generate a random realistic IP for testing
        $testIPs = [
            '*************',  // TEST-NET-3 block for documentation
            '*************',  // TEST-NET-2 block for documentation
            '***********',    // TEST-NET-1 block for documentation
            '************',   // Private network
            '********',       // Private network
            '2001:db8:85a3:8d3:1319:8a2e:370:7348' // IPv6 documentation block
        ];
        return $testIPs[array_rand($testIPs)];
    }

    // Normal IP detection for production
    $ipAddress = '';
    if (isset($_SERVER['HTTP_CLIENT_IP']))
        $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
    else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_X_FORWARDED']))
        $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
    else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
        $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_FORWARDED']))
        $ipAddress = $_SERVER['HTTP_FORWARDED'];
    else if(isset($_SERVER['REMOTE_ADDR']))
        $ipAddress = $_SERVER['REMOTE_ADDR'];
    else
        $ipAddress = 'UNKNOWN';
    return $ipAddress;
}

$userIpAddress = getClientIP();

// Remove the automatic username generation
if ($username === 'null') {
    $username = '';
}

// Generate a short hash (first 8 characters of md5)
$shortHash = substr($md5, 0, 8);

// Get current time for the database
$currentTime = date('Y-m-d H:i:s');

// Function to check if a column exists in a table
function columnExists($conn, $table, $column) {
    $query = "SHOW COLUMNS FROM `$table` LIKE '$column'";
    $result = $conn->query($query);
    return ($result && $result->num_rows > 0);
}

// Check if qrcode_data table has profile_id column
if (!columnExists($conn, 'qrcode_data', 'profile_id')) {
    // Add profile_id column if it doesn't exist
    $addColumnQuery = "ALTER TABLE qrcode_data ADD COLUMN profile_id VARCHAR(50) AFTER username";
    $conn->query($addColumnQuery);
}

// Check if qrcode_data table has ip_address column
if (!columnExists($conn, 'qrcode_data', 'ip_address')) {
    // Add ip_address column if it doesn't exist
    $addColumnQuery = "ALTER TABLE qrcode_data ADD COLUMN ip_address VARCHAR(45) DEFAULT NULL COMMENT 'User IP address when transaction was created'";
    $conn->query($addColumnQuery);
}

// Check if a record with this md5 already exists
$checkMd5Query = "SELECT id FROM qrcode_data WHERE md5 = ?";
$checkMd5Stmt = $conn->prepare($checkMd5Query);
$checkMd5Stmt->bind_param("s", $md5);
$checkMd5Stmt->execute();
$checkMd5Result = $checkMd5Stmt->get_result();

if ($checkMd5Result->num_rows > 0) {
    // Record exists, update it
    if (columnExists($conn, 'qrcode_data', 'ip_address')) {
        $updateStmt = $conn->prepare("UPDATE qrcode_data SET qr_data = ?, amount = ?, short_hash = ?, username = ?, profile_id = ?, created_at = ?, ip_address = ? WHERE md5 = ?");
        $updateStmt->bind_param("sdssssss", $qrData, $amount, $shortHash, $username, $profileId, $currentTime, $userIpAddress, $md5);
    } else {
        $updateStmt = $conn->prepare("UPDATE qrcode_data SET qr_data = ?, amount = ?, short_hash = ?, username = ?, profile_id = ?, created_at = ? WHERE md5 = ?");
        $updateStmt->bind_param("sdsssss", $qrData, $amount, $shortHash, $username, $profileId, $currentTime, $md5);
    }

    if ($updateStmt->execute()) {
        // Continue with the rest of the code (payments table, redirect, etc.)
        $recordUpdated = true;
    } else {
        echo "Error updating record: " . $updateStmt->error;
        exit;
    }
    $updateStmt->close();
} else {
    // No existing record, insert a new one
    if (columnExists($conn, 'qrcode_data', 'ip_address')) {
        $stmt = $conn->prepare("INSERT INTO qrcode_data (qr_data, amount, md5, short_hash, username, profile_id, created_at, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sdssssss", $qrData, $amount, $md5, $shortHash, $username, $profileId, $currentTime, $userIpAddress);
    } else {
        $stmt = $conn->prepare("INSERT INTO qrcode_data (qr_data, amount, md5, short_hash, username, profile_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sdsssss", $qrData, $amount, $md5, $shortHash, $username, $profileId, $currentTime);
    }

    if ($stmt->execute()) {
        $recordUpdated = true;
    } else {
        echo "Error inserting record: " . $stmt->error;
        exit;
    }
    $stmt->close();
}

$checkMd5Stmt->close();

// Only proceed if the record was successfully updated or inserted
if (isset($recordUpdated) && $recordUpdated) {
    // Insert into payments table for transaction history
    $transactionId = "TXN" . time() . rand(1000, 9999);
    $status = "pending"; // Initial status is pending

    // Check if payments table exists
    $checkTableQuery = "SHOW TABLES LIKE 'payments'";
    $checkTableResult = $conn->query($checkTableQuery);

    if ($checkTableResult->num_rows > 0) {
        // Get user_id based on profile_id
        $userQuery = "SELECT id FROM users WHERE profile_id = ?";
        $userStmt = $conn->prepare($userQuery);
        $userStmt->bind_param("s", $profileId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();

        if ($userResult->num_rows > 0) {
            $userData = $userResult->fetch_assoc();
            $userId = $userData['id'];

            // Insert payment record - only include columns we know exist
            $paymentStmt = $conn->prepare("INSERT INTO payments (user_id, amount, status, transaction_id) VALUES (?, ?, ?, ?)");

            $paymentStmt->bind_param("idss", $userId, $amount, $status, $transactionId);
            $paymentStmt->execute();
            $paymentStmt->close();
        }
        $userStmt->close();
    }

    // Redirect to qrcode.php with the parameters
    $redirectUrl = "qrcode.php?qr=" . urlencode($qrData) .
                   "&amount=" . urlencode($amount) .
                   "&md5=" . urlencode($md5) .
                   "&short_hash=" . urlencode($shortHash) .
                   "&username=" . urlencode($username) .
                   "&profile_id=" . urlencode($profileId);

    // Add company name if available
    if (!empty($companyName)) {
        $redirectUrl .= "&company_name=" . urlencode($companyName);
    }

    // Add Bakong ID if available
    if (!empty($bakongId)) {
        $redirectUrl .= "&bakong_id=" . urlencode($bakongId);
    }

    header("Location: " . $redirectUrl);
    exit;
} else {
    echo "Error: Failed to update or insert record.";
}

$conn->close();
?>