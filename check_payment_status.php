<?php
// API endpoint to check KHQR payment status
// This can be called by the C# application to check if a payment has been completed

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

// Get parameters
$bookingId = isset($_GET['booking_id']) ? intval($_GET['booking_id']) : 0;
$profileId = isset($_GET['profile_id']) ? $_GET['profile_id'] : '';
$transactionId = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';

$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

try {
    if ($bookingId > 0) {
        // Check by booking ID
        $bookingInfo = getBookingInfo($bookingId);
        
        if ($bookingInfo) {
            // Check if there's a completed KHQR payment for this booking
            $query = "SELECT * FROM payments WHERE user_id IN (
                SELECT id FROM users WHERE profile_id LIKE CONCAT('%', ?, '%')
            ) AND status = 'completed' ORDER BY date DESC LIMIT 1";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("s", $bookingId);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $response['success'] = true;
                $response['message'] = 'Payment completed';
                $response['data'] = [
                    'booking_id' => $bookingId,
                    'payment_status' => $row['status'],
                    'amount' => $row['amount'],
                    'transaction_id' => $row['transaction_id'],
                    'payer_account' => $row['payer_account'],
                    'short_hash' => $row['short_hash']
                ];
            } else {
                $response['message'] = 'Payment not completed yet';
            }
        } else {
            $response['message'] = 'Booking not found';
        }
    } elseif (!empty($profileId)) {
        // Check by profile ID
        $query = "SELECT p.*, u.profile_id FROM payments p
                 INNER JOIN users u ON p.user_id = u.id
                 WHERE u.profile_id = ?
                 ORDER BY p.date DESC LIMIT 1";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $profileId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $response['success'] = true;
            $response['message'] = 'Payment status retrieved';
            $response['data'] = [
                'profile_id' => $row['profile_id'],
                'payment_status' => $row['status'],
                'amount' => $row['amount'],
                'transaction_id' => $row['transaction_id'],
                'payer_account' => $row['payer_account'],
                'short_hash' => $row['short_hash'],
                'date' => $row['date']
            ];
        } else {
            $response['message'] = 'No payment found for this profile';
        }
    } elseif (!empty($transactionId)) {
        // Check by transaction ID
        $query = "SELECT * FROM payments WHERE transaction_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $transactionId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $response['success'] = true;
            $response['message'] = 'Payment found';
            $response['data'] = [
                'payment_status' => $row['status'],
                'amount' => $row['amount'],
                'transaction_id' => $row['transaction_id'],
                'payer_account' => $row['payer_account'],
                'short_hash' => $row['short_hash'],
                'date' => $row['date']
            ];
        } else {
            $response['message'] = 'Transaction not found';
        }
    } else {
        $response['message'] = 'Missing required parameters (booking_id, profile_id, or transaction_id)';
    }

} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    error_log("Payment status check error: " . $e->getMessage());
}

// Close database connection
$conn->close();

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
