using System;

namespace HotelManagement.Models
{
    public class Room
    {
        public int RoomId { get; set; }
        public string RoomNumber { get; set; }
        public string RoomType { get; set; }
        public decimal RatePerNight { get; set; }
        public bool IsOccupied { get; set; }
        public bool IsClean { get; set; }
        public string Description { get; set; } = "";
        public int MaxOccupancy { get; set; }
        public DateTime? LastCleanedDate { get; set; } = DateTime.Now;
        public string Status { get; set; } = "Available"; // Available, Occupied, Maintenance, Reserved

        public override string ToString()
        {
            return string.Format("Room {0} ({1}) - {2}", RoomNumber, RoomType, Status);
        }
    }
} 