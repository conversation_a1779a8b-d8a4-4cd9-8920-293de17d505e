<?php
// Check for payment notifications from KHQR system
// This endpoint checks if a payment has been completed for a specific booking/room

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

$response = [
    'success' => false,
    'payment_found' => false,
    'message' => '',
    'data' => null
];

try {
    $bookingId = isset($_GET['booking_id']) ? intval($_GET['booking_id']) : 0;
    $roomId = isset($_GET['room_id']) ? intval($_GET['room_id']) : 0;
    
    if ($bookingId <= 0 && $roomId <= 0) {
        throw new Exception('Booking ID or Room ID is required');
    }
    
    if (is_resource($conn)) {
        // SQL Server - not implemented for this demo
        $response['message'] = 'SQL Server notification checking not implemented';
    } else {
        // MySQL - check notification table
        $query = "SELECT * FROM hotel_payment_notifications WHERE ";
        $params = [];
        $types = "";
        
        if ($bookingId > 0 && $roomId > 0) {
            $query .= "booking_id = ? AND room_id = ?";
            $params = [$bookingId, $roomId];
            $types = "ii";
        } elseif ($bookingId > 0) {
            $query .= "booking_id = ?";
            $params = [$bookingId];
            $types = "i";
        } else {
            $query .= "room_id = ?";
            $params = [$roomId];
            $types = "i";
        }
        
        $query .= " ORDER BY created_at DESC LIMIT 1";
        
        $stmt = $conn->prepare($query);
        if ($params) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $response['success'] = true;
            $response['payment_found'] = true;
            $response['message'] = 'Payment notification found';
            $response['data'] = [
                'booking_id' => $row['booking_id'],
                'room_id' => $row['room_id'],
                'guest_name' => $row['guest_name'],
                'amount' => $row['amount'],
                'payer_account' => $row['payer_account'],
                'short_hash' => $row['short_hash'],
                'md5' => $row['md5'],
                'created_at' => $row['created_at'],
                'processed' => $row['processed']
            ];
            
            // Mark as processed
            $updateQuery = "UPDATE hotel_payment_notifications SET processed = TRUE WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("i", $row['id']);
            $updateStmt->execute();
            $updateStmt->close();
            
        } else {
            $response['success'] = true;
            $response['payment_found'] = false;
            $response['message'] = 'No payment notification found';
        }
        
        $stmt->close();
    }
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    error_log("Payment notification check error: " . $e->getMessage());
}

// Close database connection
if (is_resource($conn)) {
    sqlsrv_close($conn);
} else {
    $conn->close();
}

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
