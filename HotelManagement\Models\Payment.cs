using System;

namespace HotelManagement.Models
{
    public class Payment
    {
        public int PaymentId { get; set; }
        public int BookingId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } // Cash, Credit Card, Debit Card, Bank Transfer
        public DateTime PaymentDate { get; set; }
        public string PaymentStatus { get; set; } // Completed, Pending, Failed, Refunded
        public string TransactionReference { get; set; }
        public string Notes { get; set; }
        public int ProcessedByStaffId { get; set; }
        public Booking Booking { get; set; }

        public override string ToString()
        {
            return string.Format("Payment #{0} - {1:C} - {2} - {3}", PaymentId, Amount, PaymentMethod, PaymentStatus);
        }
    }
} 