<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KHQR Hotel Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏨 KHQR Hotel Payment Integration Test</h1>
        
        <div class="info-box">
            <h3>How it works:</h3>
            <ol>
                <li><strong>C# Hotel App</strong> - User selects "KHQR Cash" payment method</li>
                <li><strong>Generate KHQR</strong> - System creates QR code with room ID and booking details</li>
                <li><strong>User Scans</strong> - Customer scans QR with banking app and pays</li>
                <li><strong>Auto Detection</strong> - System detects payment via NBC Bakong API</li>
                <li><strong>Complete Payment</strong> - C# app automatically completes the payment</li>
            </ol>
        </div>

        <h2>Test KHQR Payment Generation</h2>
        <form id="khqrForm">
            <div class="form-group">
                <label for="amount">Amount (USD):</label>
                <input type="number" id="amount" step="0.01" value="25.00" required>
            </div>
            
            <div class="form-group">
                <label for="roomId">Room ID:</label>
                <input type="number" id="roomId" value="101" required>
            </div>
            
            <div class="form-group">
                <label for="bookingId">Booking ID:</label>
                <input type="number" id="bookingId" value="1001" required>
            </div>
            
            <div class="form-group">
                <label for="guestName">Guest Name:</label>
                <input type="text" id="guestName" value="John Doe" required>
            </div>
            
            <div class="form-group">
                <label for="roomNumber">Room Number:</label>
                <input type="text" id="roomNumber" value="A-101" required>
            </div>
            
            <button type="button" onclick="generateKHQR()">🔗 Generate KHQR Payment</button>
            <button type="button" onclick="checkPaymentStatus()">✅ Check Payment Status</button>
        </form>

        <div id="result" style="margin-top: 20px;"></div>

        <div class="info-box">
            <h3>🔧 Integration Points:</h3>
            <ul>
                <li><strong>request.php</strong> - Generates KHQR using Bakong API</li>
                <li><strong>save.php</strong> - Saves payment data with room ID</li>
                <li><strong>qrcode.php</strong> - Displays QR and monitors payment</li>
                <li><strong>update_payment_status.php</strong> - Updates when payment completes</li>
                <li><strong>check_payment_notification.php</strong> - C# app checks this endpoint</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>📱 C# Integration:</h3>
            <p>In your PaymentForm, when "KHQR Cash" is selected:</p>
            <ol>
                <li>WebBrowser control loads: <code>request.php?amount=25.00&room_id=101&booking_id=1001&guest_name=John%20Doe&username=A-101</code></li>
                <li>Background monitoring checks: <code>check_payment_notification.php?booking_id=1001&room_id=101</code></li>
                <li>When payment detected, automatically completes in C# database</li>
            </ol>
        </div>
    </div>

    <script>
        function generateKHQR() {
            const amount = document.getElementById('amount').value;
            const roomId = document.getElementById('roomId').value;
            const bookingId = document.getElementById('bookingId').value;
            const guestName = document.getElementById('guestName').value;
            const roomNumber = document.getElementById('roomNumber').value;
            
            if (!amount || !roomId || !bookingId || !guestName || !roomNumber) {
                showResult('Please fill in all fields', 'error');
                return;
            }
            
            const url = `request.php?amount=${amount}&room_id=${roomId}&booking_id=${bookingId}&guest_name=${encodeURIComponent(guestName)}&username=${encodeURIComponent(roomNumber)}&company_name=${encodeURIComponent('Hotel Management System')}&profile_id=${encodeURIComponent('room_' + roomId)}`;
            
            showResult(`
                <h3>🚀 KHQR Payment Generated!</h3>
                <p><strong>URL:</strong> <a href="${url}" target="_blank">${url}</a></p>
                <p>Click the link above to open the KHQR payment page in a new window.</p>
                <p><em>This is what your C# WebBrowser control would display.</em></p>
            `, 'success');
        }
        
        async function checkPaymentStatus() {
            const bookingId = document.getElementById('bookingId').value;
            const roomId = document.getElementById('roomId').value;
            
            if (!bookingId || !roomId) {
                showResult('Please enter Booking ID and Room ID', 'error');
                return;
            }
            
            try {
                const response = await fetch(`check_payment_notification.php?booking_id=${bookingId}&room_id=${roomId}`);
                const data = await response.json();
                
                if (data.success && data.payment_found) {
                    showResult(`
                        <h3>✅ Payment Found!</h3>
                        <p><strong>Guest:</strong> ${data.data.guest_name}</p>
                        <p><strong>Amount:</strong> $${data.data.amount}</p>
                        <p><strong>Payer:</strong> ${data.data.payer_account}</p>
                        <p><strong>Hash:</strong> ${data.data.short_hash}</p>
                        <p><strong>Date:</strong> ${data.data.created_at}</p>
                    `, 'success');
                } else {
                    showResult(`
                        <h3>⏳ No Payment Found</h3>
                        <p>No completed payment found for Booking ID ${bookingId} and Room ID ${roomId}</p>
                        <p><em>Payment may still be pending or not yet initiated.</em></p>
                    `, 'info-box');
                }
            } catch (error) {
                showResult(`Error checking payment status: ${error.message}`, 'error');
            }
        }
        
        function showResult(html, className) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="${className}">${html}</div>`;
        }
    </script>
</body>
</html>
