using System;
using System.Collections.Generic;

namespace HotelManagement.Models
{
    public class Booking
    {
        public int BookingId { get; set; }
        public int GuestId { get; set; }
        public int RoomId { get; set; }
        public Guest Guest { get; set; }
        public Room Room { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public DateTime? ActualCheckInDate { get; set; }
        public DateTime? ActualCheckOutDate { get; set; }
        public int NumberOfAdults { get; set; }
        public int NumberOfChildren { get; set; }
        public string BookingStatus { get; set; } // Confirmed, Checked-In, Checked-Out, Cancelled, No-Show
        public decimal TotalAmount { get; set; }
        public bool IsPaid { get; set; }
        public string BookingSource { get; set; } // Direct, Website, Travel Agent, etc.
        public string Notes { get; set; }
        public DateTime BookingDate { get; set; }
        public int BookedByStaffId { get; set; }
        
        // Additional properties for UI display
        public string GuestName { get; set; }
        public string RoomNumber { get; set; }

        public int GetNumberOfNights()
        {
            return (CheckOutDate - CheckInDate).Days;
        }
        
        public decimal CalculateCurrentAmount()
        {
            if (ActualCheckInDate.HasValue && Room != null)
            {
                TimeSpan duration = DateTime.Now - ActualCheckInDate.Value;
                int days = (int)Math.Ceiling(duration.TotalDays);
                if (days < 1) days = 1; // Minimum 1 day stay
                
                return Room.RatePerNight * days;
            }
            
            return TotalAmount;
        }

        public override string ToString()
        {
            return string.Format("Booking #{0} - {1} - {2} - {3}", 
                BookingId, 
                Guest != null ? Guest.FullName : (GuestName != null ? GuestName : "Unknown"), 
                Room != null ? Room.RoomNumber : (RoomNumber != null ? RoomNumber : "Unknown"), 
                BookingStatus);
        }
    }
} 