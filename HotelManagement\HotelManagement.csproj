<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B1F9E6A1-3A4D-4F2F-9E3B-2E8F5B7C5D0D}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>HotelManagement</RootNamespace>
    <AssemblyName>HotelManagement</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="FontAwesome.Sharp, Version=5.15.4.0, Culture=neutral, PublicKeyToken=d16d1e4e568ec10f, processorArchitecture=MSIL">
      <HintPath>..\packages\FontAwesome.Sharp.5.15.4\lib\net472\FontAwesome.Sharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\RoomManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\RoomManagementForm.Designer.cs">
      <DependentUpon>RoomManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\StaffManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StaffManagementForm.Designer.cs">
      <DependentUpon>StaffManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CheckInForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CheckInForm.Designer.cs">
      <DependentUpon>CheckInForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CheckOutForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CheckOutForm.Designer.cs">
      <DependentUpon>CheckOutForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PaymentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PaymentForm.Designer.cs">
      <DependentUpon>PaymentForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\GuestManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\GuestManagementForm.Designer.cs">
      <DependentUpon>GuestManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BookingForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BookingForm.Designer.cs">
      <DependentUpon>BookingForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\Room.cs" />
    <Compile Include="Models\Staff.cs" />
    <Compile Include="Models\Guest.cs" />
    <Compile Include="Models\Booking.cs" />
    <Compile Include="Models\Payment.cs" />
    <Compile Include="Data\DatabaseManager.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\RoomManagementForm.resx">
      <DependentUpon>RoomManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\StaffManagementForm.resx">
      <DependentUpon>StaffManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CheckInForm.resx">
      <DependentUpon>CheckInForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CheckOutForm.resx">
      <DependentUpon>CheckOutForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PaymentForm.resx">
      <DependentUpon>PaymentForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\GuestManagementForm.resx">
      <DependentUpon>GuestManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BookingForm.resx">
      <DependentUpon>BookingForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project> 