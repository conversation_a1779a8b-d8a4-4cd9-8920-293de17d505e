/*! For license information please see khqr-1.0.6.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.BakongKHQR=e():t.BakongKHQR=e()}(self,(function(){return t={742:(t,e)=>{"use strict";e.byteLength=function(t){var e=a(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){var e,n,i=a(t),s=i[0],u=i[1],c=new o(function(t,e,n){return 3*(e+n)/4-n}(0,s,u)),l=0,f=u>0?s-4:s;for(n=0;n<f;n+=4)e=r[t.charCodeAt(n)]<<18|r[t.charCodeAt(n+1)]<<12|r[t.charCodeAt(n+2)]<<6|r[t.charCodeAt(n+3)],c[l++]=e>>16&255,c[l++]=e>>8&255,c[l++]=255&e;return 2===u&&(e=r[t.charCodeAt(n)]<<2|r[t.charCodeAt(n+1)]>>4,c[l++]=255&e),1===u&&(e=r[t.charCodeAt(n)]<<10|r[t.charCodeAt(n+1)]<<4|r[t.charCodeAt(n+2)]>>2,c[l++]=e>>8&255,c[l++]=255&e),c},e.fromByteArray=function(t){for(var e,r=t.length,o=r%3,i=[],s=16383,u=0,a=r-o;u<a;u+=s)i.push(c(t,u,u+s>a?a:u+s));return 1===o?(e=t[r-1],i.push(n[e>>2]+n[e<<4&63]+"==")):2===o&&(e=(t[r-2]<<8)+t[r-1],i.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"=")),i.join("")};for(var n=[],r=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,u=i.length;s<u;++s)n[s]=i[s],r[i.charCodeAt(s)]=s;function a(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function c(t,e,r){for(var o,i,s=[],u=e;u<r;u+=3)o=(t[u]<<16&16711680)+(t[u+1]<<8&65280)+(255&t[u+2]),s.push(n[(i=o)>>18&63]+n[i>>12&63]+n[i>>6&63]+n[63&i]);return s.join("")}r["-".charCodeAt(0)]=62,r["_".charCodeAt(0)]=63},764:(t,e,n)=>{"use strict";const r=n(742),o=n(645),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.lW=a,e.h2=50;const s=2147483647;function u(t){if(t>s)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,n){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return f(t)}return c(t,e,n)}function c(t,e,n){if("string"==typeof t)return function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!a.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const n=0|I(t,e);let r=u(n);const o=r.write(t,e);return o!==n&&(r=r.slice(0,o)),r}(t,e);if(ArrayBuffer.isView(t))return function(t){if(z(t,Uint8Array)){const e=new Uint8Array(t);return p(e.buffer,e.byteOffset,e.byteLength)}return h(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(z(t,ArrayBuffer)||t&&z(t.buffer,ArrayBuffer))return p(t,e,n);if("undefined"!=typeof SharedArrayBuffer&&(z(t,SharedArrayBuffer)||t&&z(t.buffer,SharedArrayBuffer)))return p(t,e,n);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');const r=t.valueOf&&t.valueOf();if(null!=r&&r!==t)return a.from(r,e,n);const o=function(t){if(a.isBuffer(t)){const e=0|g(t.length),n=u(e);return 0===n.length||t.copy(n,0,0,e),n}return void 0!==t.length?"number"!=typeof t.length||W(t.length)?u(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(o)return o;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function l(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function f(t){return l(t),u(t<0?0:0|g(t))}function h(t){const e=t.length<0?0:0|g(t.length),n=u(e);for(let r=0;r<e;r+=1)n[r]=255&t[r];return n}function p(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');let r;return r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n),Object.setPrototypeOf(r,a.prototype),r}function g(t){if(t>=s)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s.toString(16)+" bytes");return 0|t}function I(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||z(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const n=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;let o=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return Y(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return j(t).length;default:if(o)return r?-1:Y(t).length;e=(""+e).toLowerCase(),o=!0}}function A(t,e,n){let r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return D(this,e,n);case"utf8":case"utf-8":return b(this,e,n);case"ascii":return L(this,e,n);case"latin1":case"binary":return w(this,e,n);case"base64":return m(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function N(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function d(t,e,n,r,o){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),W(n=+n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof e&&(e=a.from(e,r)),a.isBuffer(e))return 0===e.length?-1:T(t,e,n,r,o);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):T(t,[e],n,r,o);throw new TypeError("val must be string, number or Buffer")}function T(t,e,n,r,o){let i,s=1,u=t.length,a=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;s=2,u/=2,a/=2,n/=2}function c(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(o){let r=-1;for(i=n;i<u;i++)if(c(t,i)===c(e,-1===r?0:i-r)){if(-1===r&&(r=i),i-r+1===a)return r*s}else-1!==r&&(i-=i-r),r=-1}else for(n+a>u&&(n=u-a),i=n;i>=0;i--){let n=!0;for(let r=0;r<a;r++)if(c(t,i+r)!==c(e,r)){n=!1;break}if(n)return i}return-1}function _(t,e,n,r){n=Number(n)||0;const o=t.length-n;r?(r=Number(r))>o&&(r=o):r=o;const i=e.length;let s;for(r>i/2&&(r=i/2),s=0;s<r;++s){const r=parseInt(e.substr(2*s,2),16);if(W(r))return s;t[n+s]=r}return s}function E(t,e,n,r){return $(Y(e,t.length-n),t,n,r)}function y(t,e,n,r){return $(function(t){const e=[];for(let n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function R(t,e,n,r){return $(j(e),t,n,r)}function C(t,e,n,r){return $(function(t,e){let n,r,o;const i=[];for(let s=0;s<t.length&&!((e-=2)<0);++s)n=t.charCodeAt(s),r=n>>8,o=n%256,i.push(o),i.push(r);return i}(e,t.length-n),t,n,r)}function m(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function b(t,e,n){n=Math.min(t.length,n);const r=[];let o=e;for(;o<n;){const e=t[o];let i=null,s=e>239?4:e>223?3:e>191?2:1;if(o+s<=n){let n,r,u,a;switch(s){case 1:e<128&&(i=e);break;case 2:n=t[o+1],128==(192&n)&&(a=(31&e)<<6|63&n,a>127&&(i=a));break;case 3:n=t[o+1],r=t[o+2],128==(192&n)&&128==(192&r)&&(a=(15&e)<<12|(63&n)<<6|63&r,a>2047&&(a<55296||a>57343)&&(i=a));break;case 4:n=t[o+1],r=t[o+2],u=t[o+3],128==(192&n)&&128==(192&r)&&128==(192&u)&&(a=(15&e)<<18|(63&n)<<12|(63&r)<<6|63&u,a>65535&&a<1114112&&(i=a))}}null===i?(i=65533,s=1):i>65535&&(i-=65536,r.push(i>>>10&1023|55296),i=56320|1023&i),r.push(i),o+=s}return function(t){const e=t.length;if(e<=O)return String.fromCharCode.apply(String,t);let n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=O));return n}(r)}a.TYPED_ARRAY_SUPPORT=function(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,n){return c(t,e,n)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,n){return function(t,e,n){return l(t),t<=0?u(t):void 0!==e?"string"==typeof n?u(t).fill(e,n):u(t).fill(e):u(t)}(t,e,n)},a.allocUnsafe=function(t){return f(t)},a.allocUnsafeSlow=function(t){return f(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(z(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),z(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,r=e.length;for(let o=0,i=Math.min(n,r);o<i;++o)if(t[o]!==e[o]){n=t[o],r=e[o];break}return n<r?-1:r<n?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);let n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;const r=a.allocUnsafe(e);let o=0;for(n=0;n<t.length;++n){let e=t[n];if(z(e,Uint8Array))o+e.length>r.length?(a.isBuffer(e)||(e=a.from(e)),e.copy(r,o)):Uint8Array.prototype.set.call(r,e,o);else{if(!a.isBuffer(e))throw new TypeError('"list" argument must be an Array of Buffers');e.copy(r,o)}o+=e.length}return r},a.byteLength=I,a.prototype._isBuffer=!0,a.prototype.swap16=function(){const t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)N(this,e,e+1);return this},a.prototype.swap32=function(){const t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)N(this,e,e+3),N(this,e+1,e+2);return this},a.prototype.swap64=function(){const t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)N(this,e,e+7),N(this,e+1,e+6),N(this,e+2,e+5),N(this,e+3,e+4);return this},a.prototype.toString=function(){const t=this.length;return 0===t?"":0===arguments.length?b(this,0,t):A.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){let t="";const n=e.h2;return t=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(t+=" ... "),"<Buffer "+t+">"},i&&(a.prototype[i]=a.prototype.inspect),a.prototype.compare=function(t,e,n,r,o){if(z(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),e<0||n>t.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&e>=n)return 0;if(r>=o)return-1;if(e>=n)return 1;if(this===t)return 0;let i=(o>>>=0)-(r>>>=0),s=(n>>>=0)-(e>>>=0);const u=Math.min(i,s),c=this.slice(r,o),l=t.slice(e,n);for(let t=0;t<u;++t)if(c[t]!==l[t]){i=c[t],s=l[t];break}return i<s?-1:s<i?1:0},a.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},a.prototype.indexOf=function(t,e,n){return d(this,t,e,n,!0)},a.prototype.lastIndexOf=function(t,e,n){return d(this,t,e,n,!1)},a.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}const o=this.length-e;if((void 0===n||n>o)&&(n=o),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let i=!1;for(;;)switch(r){case"hex":return _(this,t,e,n);case"utf8":case"utf-8":return E(this,t,e,n);case"ascii":case"latin1":case"binary":return y(this,t,e,n);case"base64":return R(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,t,e,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const O=4096;function L(t,e,n){let r="";n=Math.min(t.length,n);for(let o=e;o<n;++o)r+=String.fromCharCode(127&t[o]);return r}function w(t,e,n){let r="";n=Math.min(t.length,n);for(let o=e;o<n;++o)r+=String.fromCharCode(t[o]);return r}function D(t,e,n){const r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);let o="";for(let r=e;r<n;++r)o+=J[t[r]];return o}function B(t,e,n){const r=t.slice(e,n);let o="";for(let t=0;t<r.length-1;t+=2)o+=String.fromCharCode(r[t]+256*r[t+1]);return o}function U(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function M(t,e,n,r,o,i){if(!a.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<i)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function v(t,e,n,r,o){P(e,r,o,t,n,7);let i=Number(e&BigInt(4294967295));t[n++]=i,i>>=8,t[n++]=i,i>>=8,t[n++]=i,i>>=8,t[n++]=i;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[n++]=s,s>>=8,t[n++]=s,s>>=8,t[n++]=s,s>>=8,t[n++]=s,n}function H(t,e,n,r,o){P(e,r,o,t,n,7);let i=Number(e&BigInt(4294967295));t[n+7]=i,i>>=8,t[n+6]=i,i>>=8,t[n+5]=i,i>>=8,t[n+4]=i;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[n+3]=s,s>>=8,t[n+2]=s,s>>=8,t[n+1]=s,s>>=8,t[n]=s,n+8}function G(t,e,n,r,o,i){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function S(t,e,n,r,i){return e=+e,n>>>=0,i||G(t,0,n,4),o.write(t,e,n,r,23,4),n+4}function x(t,e,n,r,i){return e=+e,n>>>=0,i||G(t,0,n,8),o.write(t,e,n,r,52,8),n+8}a.prototype.slice=function(t,e){const n=this.length;(t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);const r=this.subarray(t,e);return Object.setPrototypeOf(r,a.prototype),r},a.prototype.readUintLE=a.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=this[t],o=1,i=0;for(;++i<e&&(o*=256);)r+=this[t+i]*o;return r},a.prototype.readUintBE=a.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=this[t+--e],o=1;for(;e>0&&(o*=256);)r+=this[t+--e]*o;return r},a.prototype.readUint8=a.prototype.readUInt8=function(t,e){return t>>>=0,e||U(t,1,this.length),this[t]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||U(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||U(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||U(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||U(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readBigUInt64LE=Z((function(t){Q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||K(t,this.length-8);const r=e+256*this[++t]+65536*this[++t]+this[++t]*2**24,o=this[++t]+256*this[++t]+65536*this[++t]+n*2**24;return BigInt(r)+(BigInt(o)<<BigInt(32))})),a.prototype.readBigUInt64BE=Z((function(t){Q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||K(t,this.length-8);const r=e*2**24+65536*this[++t]+256*this[++t]+this[++t],o=this[++t]*2**24+65536*this[++t]+256*this[++t]+n;return(BigInt(r)<<BigInt(32))+BigInt(o)})),a.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=this[t],o=1,i=0;for(;++i<e&&(o*=256);)r+=this[t+i]*o;return o*=128,r>=o&&(r-=Math.pow(2,8*e)),r},a.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=e,o=1,i=this[t+--r];for(;r>0&&(o*=256);)i+=this[t+--r]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*e)),i},a.prototype.readInt8=function(t,e){return t>>>=0,e||U(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||U(t,2,this.length);const n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt16BE=function(t,e){t>>>=0,e||U(t,2,this.length);const n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||U(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||U(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readBigInt64LE=Z((function(t){Q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||K(t,this.length-8);const r=this[t+4]+256*this[t+5]+65536*this[t+6]+(n<<24);return(BigInt(r)<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+this[++t]*2**24)})),a.prototype.readBigInt64BE=Z((function(t){Q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||K(t,this.length-8);const r=(e<<24)+65536*this[++t]+256*this[++t]+this[++t];return(BigInt(r)<<BigInt(32))+BigInt(this[++t]*2**24+65536*this[++t]+256*this[++t]+n)})),a.prototype.readFloatLE=function(t,e){return t>>>=0,e||U(t,4,this.length),o.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||U(t,4,this.length),o.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||U(t,8,this.length),o.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||U(t,8,this.length),o.read(this,t,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||M(this,t,e,n,Math.pow(2,8*n)-1,0);let o=1,i=0;for(this[e]=255&t;++i<n&&(o*=256);)this[e+i]=t/o&255;return e+n},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||M(this,t,e,n,Math.pow(2,8*n)-1,0);let o=n-1,i=1;for(this[e+o]=255&t;--o>=0&&(i*=256);)this[e+o]=t/i&255;return e+n},a.prototype.writeUint8=a.prototype.writeUInt8=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeBigUInt64LE=Z((function(t,e=0){return v(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),a.prototype.writeBigUInt64BE=Z((function(t,e=0){return H(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),a.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e>>>=0,!r){const r=Math.pow(2,8*n-1);M(this,t,e,n,r-1,-r)}let o=0,i=1,s=0;for(this[e]=255&t;++o<n&&(i*=256);)t<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(t/i>>0)-s&255;return e+n},a.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e>>>=0,!r){const r=Math.pow(2,8*n-1);M(this,t,e,n,r-1,-r)}let o=n-1,i=1,s=0;for(this[e+o]=255&t;--o>=0&&(i*=256);)t<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(t/i>>0)-s&255;return e+n},a.prototype.writeInt8=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,n){return t=+t,e>>>=0,n||M(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeBigInt64LE=Z((function(t,e=0){return v(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),a.prototype.writeBigInt64BE=Z((function(t,e=0){return H(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),a.prototype.writeFloatLE=function(t,e,n){return S(this,t,e,!0,n)},a.prototype.writeFloatBE=function(t,e,n){return S(this,t,e,!1,n)},a.prototype.writeDoubleLE=function(t,e,n){return x(this,t,e,!0,n)},a.prototype.writeDoubleBE=function(t,e,n){return x(this,t,e,!1,n)},a.prototype.copy=function(t,e,n,r){if(!a.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);const o=r-n;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(t,this.subarray(n,r),e),o},a.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!a.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(1===t.length){const e=t.charCodeAt(0);("utf8"===r&&e<128||"latin1"===r)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;let o;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(o=e;o<n;++o)this[o]=t;else{const i=a.isBuffer(t)?t:a.from(t,r),s=i.length;if(0===s)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<n-e;++o)this[o+e]=i[o%s]}return this};const k={};function V(t,e,n){k[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function F(t){let e="",n=t.length;const r="-"===t[0]?1:0;for(;n>=r+4;n-=3)e=`_${t.slice(n-3,n)}${e}`;return`${t.slice(0,n)}${e}`}function P(t,e,n,r,o,i){if(t>n||t<e){const r="bigint"==typeof e?"n":"";let o;throw o=i>3?0===e||e===BigInt(0)?`>= 0${r} and < 2${r} ** ${8*(i+1)}${r}`:`>= -(2${r} ** ${8*(i+1)-1}${r}) and < 2 ** ${8*(i+1)-1}${r}`:`>= ${e}${r} and <= ${n}${r}`,new k.ERR_OUT_OF_RANGE("value",o,t)}!function(t,e,n){Q(e,"offset"),void 0!==t[e]&&void 0!==t[e+n]||K(e,t.length-(n+1))}(r,o,i)}function Q(t,e){if("number"!=typeof t)throw new k.ERR_INVALID_ARG_TYPE(e,"number",t)}function K(t,e,n){if(Math.floor(t)!==t)throw Q(t,n),new k.ERR_OUT_OF_RANGE(n||"offset","an integer",t);if(e<0)throw new k.ERR_BUFFER_OUT_OF_BOUNDS;throw new k.ERR_OUT_OF_RANGE(n||"offset",`>= ${n?1:0} and <= ${e}`,t)}V("ERR_BUFFER_OUT_OF_BOUNDS",(function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),V("ERR_INVALID_ARG_TYPE",(function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`}),TypeError),V("ERR_OUT_OF_RANGE",(function(t,e,n){let r=`The value of "${t}" is out of range.`,o=n;return Number.isInteger(n)&&Math.abs(n)>2**32?o=F(String(n)):"bigint"==typeof n&&(o=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(o=F(o)),o+="n"),r+=` It must be ${e}. Received ${o}`,r}),RangeError);const q=/[^+/0-9A-Za-z-_]/g;function Y(t,e){let n;e=e||1/0;const r=t.length;let o=null;const i=[];for(let s=0;s<r;++s){if(n=t.charCodeAt(s),n>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(s+1===r){(e-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;i.push(n)}else if(n<2048){if((e-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function j(t){return r.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(q,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function $(t,e,n,r){let o;for(o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}function z(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function W(t){return t!=t}const J=function(){const t="0123456789abcdef",e=new Array(256);for(let n=0;n<16;++n){const r=16*n;for(let o=0;o<16;++o)e[r+o]=t[n]+t[o]}return e}();function Z(t){return"undefined"==typeof BigInt?X:t}function X(){throw new Error("BigInt not supported")}},487:t=>{var e={utf8:{stringToBytes:function(t){return e.bin.stringToBytes(unescape(encodeURIComponent(t)))},bytesToString:function(t){return decodeURIComponent(escape(e.bin.bytesToString(t)))}},bin:{stringToBytes:function(t){for(var e=[],n=0;n<t.length;n++)e.push(255&t.charCodeAt(n));return e},bytesToString:function(t){for(var e=[],n=0;n<t.length;n++)e.push(String.fromCharCode(t[n]));return e.join("")}}};t.exports=e},12:t=>{var e,n;e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(t,e){return t<<e|t>>>32-e},rotr:function(t,e){return t<<32-e|t>>>e},endian:function(t){if(t.constructor==Number)return 16711935&n.rotl(t,8)|4278255360&n.rotl(t,24);for(var e=0;e<t.length;e++)t[e]=n.endian(t[e]);return t},randomBytes:function(t){for(var e=[];t>0;t--)e.push(Math.floor(256*Math.random()));return e},bytesToWords:function(t){for(var e=[],n=0,r=0;n<t.length;n++,r+=8)e[r>>>5]|=t[n]<<24-r%32;return e},wordsToBytes:function(t){for(var e=[],n=0;n<32*t.length;n+=8)e.push(t[n>>>5]>>>24-n%32&255);return e},bytesToHex:function(t){for(var e=[],n=0;n<t.length;n++)e.push((t[n]>>>4).toString(16)),e.push((15&t[n]).toString(16));return e.join("")},hexToBytes:function(t){for(var e=[],n=0;n<t.length;n+=2)e.push(parseInt(t.substr(n,2),16));return e},bytesToBase64:function(t){for(var n=[],r=0;r<t.length;r+=3)for(var o=t[r]<<16|t[r+1]<<8|t[r+2],i=0;i<4;i++)8*r+6*i<=8*t.length?n.push(e.charAt(o>>>6*(3-i)&63)):n.push("=");return n.join("")},base64ToBytes:function(t){t=t.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,o=0;r<t.length;o=++r%4)0!=o&&n.push((e.indexOf(t.charAt(r-1))&Math.pow(2,-2*o+8)-1)<<2*o|e.indexOf(t.charAt(r))>>>6-2*o);return n}},t.exports=n},645:(t,e)=>{e.read=function(t,e,n,r,o){var i,s,u=8*o-r-1,a=(1<<u)-1,c=a>>1,l=-7,f=n?o-1:0,h=n?-1:1,p=t[e+f];for(f+=h,i=p&(1<<-l)-1,p>>=-l,l+=u;l>0;i=256*i+t[e+f],f+=h,l-=8);for(s=i&(1<<-l)-1,i>>=-l,l+=r;l>0;s=256*s+t[e+f],f+=h,l-=8);if(0===i)i=1-c;else{if(i===a)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,r),i-=c}return(p?-1:1)*s*Math.pow(2,i-r)},e.write=function(t,e,n,r,o,i){var s,u,a,c=8*i-o-1,l=(1<<c)-1,f=l>>1,h=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:i-1,g=r?1:-1,I=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,s=l):(s=Math.floor(Math.log(e)/Math.LN2),e*(a=Math.pow(2,-s))<1&&(s--,a*=2),(e+=s+f>=1?h/a:h*Math.pow(2,1-f))*a>=2&&(s++,a/=2),s+f>=l?(u=0,s=l):s+f>=1?(u=(e*a-1)*Math.pow(2,o),s+=f):(u=e*Math.pow(2,f-1)*Math.pow(2,o),s=0));o>=8;t[n+p]=255&u,p+=g,u/=256,o-=8);for(s=s<<o|u,c+=o;c>0;t[n+p]=255&s,p+=g,s/=256,c-=8);t[n+p-g]|=128*I}},738:t=>{function e(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t){return null!=t&&(e(t)||function(t){return"function"==typeof t.readFloatLE&&"function"==typeof t.slice&&e(t.slice(0,0))}(t)||!!t._isBuffer)}},568:(t,e,n)=>{var r,o,i,s,u;r=n(12),o=n(487).utf8,i=n(738),s=n(487).bin,(u=function(t,e){t.constructor==String?t=e&&"binary"===e.encoding?s.stringToBytes(t):o.stringToBytes(t):i(t)?t=Array.prototype.slice.call(t,0):Array.isArray(t)||t.constructor===Uint8Array||(t=t.toString());for(var n=r.bytesToWords(t),a=8*t.length,c=1732584193,l=-271733879,f=-1732584194,h=271733878,p=0;p<n.length;p++)n[p]=16711935&(n[p]<<8|n[p]>>>24)|4278255360&(n[p]<<24|n[p]>>>8);n[a>>>5]|=128<<a%32,n[14+(a+64>>>9<<4)]=a;var g=u._ff,I=u._gg,A=u._hh,N=u._ii;for(p=0;p<n.length;p+=16){var d=c,T=l,_=f,E=h;c=g(c,l,f,h,n[p+0],7,-680876936),h=g(h,c,l,f,n[p+1],12,-389564586),f=g(f,h,c,l,n[p+2],17,606105819),l=g(l,f,h,c,n[p+3],22,-1044525330),c=g(c,l,f,h,n[p+4],7,-176418897),h=g(h,c,l,f,n[p+5],12,1200080426),f=g(f,h,c,l,n[p+6],17,-1473231341),l=g(l,f,h,c,n[p+7],22,-45705983),c=g(c,l,f,h,n[p+8],7,1770035416),h=g(h,c,l,f,n[p+9],12,-1958414417),f=g(f,h,c,l,n[p+10],17,-42063),l=g(l,f,h,c,n[p+11],22,-1990404162),c=g(c,l,f,h,n[p+12],7,1804603682),h=g(h,c,l,f,n[p+13],12,-40341101),f=g(f,h,c,l,n[p+14],17,-1502002290),c=I(c,l=g(l,f,h,c,n[p+15],22,1236535329),f,h,n[p+1],5,-165796510),h=I(h,c,l,f,n[p+6],9,-1069501632),f=I(f,h,c,l,n[p+11],14,643717713),l=I(l,f,h,c,n[p+0],20,-373897302),c=I(c,l,f,h,n[p+5],5,-701558691),h=I(h,c,l,f,n[p+10],9,38016083),f=I(f,h,c,l,n[p+15],14,-660478335),l=I(l,f,h,c,n[p+4],20,-405537848),c=I(c,l,f,h,n[p+9],5,568446438),h=I(h,c,l,f,n[p+14],9,-1019803690),f=I(f,h,c,l,n[p+3],14,-187363961),l=I(l,f,h,c,n[p+8],20,1163531501),c=I(c,l,f,h,n[p+13],5,-1444681467),h=I(h,c,l,f,n[p+2],9,-51403784),f=I(f,h,c,l,n[p+7],14,1735328473),c=A(c,l=I(l,f,h,c,n[p+12],20,-1926607734),f,h,n[p+5],4,-378558),h=A(h,c,l,f,n[p+8],11,-2022574463),f=A(f,h,c,l,n[p+11],16,1839030562),l=A(l,f,h,c,n[p+14],23,-35309556),c=A(c,l,f,h,n[p+1],4,-1530992060),h=A(h,c,l,f,n[p+4],11,1272893353),f=A(f,h,c,l,n[p+7],16,-155497632),l=A(l,f,h,c,n[p+10],23,-1094730640),c=A(c,l,f,h,n[p+13],4,681279174),h=A(h,c,l,f,n[p+0],11,-358537222),f=A(f,h,c,l,n[p+3],16,-722521979),l=A(l,f,h,c,n[p+6],23,76029189),c=A(c,l,f,h,n[p+9],4,-640364487),h=A(h,c,l,f,n[p+12],11,-421815835),f=A(f,h,c,l,n[p+15],16,530742520),c=N(c,l=A(l,f,h,c,n[p+2],23,-995338651),f,h,n[p+0],6,-198630844),h=N(h,c,l,f,n[p+7],10,1126891415),f=N(f,h,c,l,n[p+14],15,-1416354905),l=N(l,f,h,c,n[p+5],21,-57434055),c=N(c,l,f,h,n[p+12],6,1700485571),h=N(h,c,l,f,n[p+3],10,-1894986606),f=N(f,h,c,l,n[p+10],15,-1051523),l=N(l,f,h,c,n[p+1],21,-2054922799),c=N(c,l,f,h,n[p+8],6,1873313359),h=N(h,c,l,f,n[p+15],10,-30611744),f=N(f,h,c,l,n[p+6],15,-1560198380),l=N(l,f,h,c,n[p+13],21,1309151649),c=N(c,l,f,h,n[p+4],6,-145523070),h=N(h,c,l,f,n[p+11],10,-1120210379),f=N(f,h,c,l,n[p+2],15,718787259),l=N(l,f,h,c,n[p+9],21,-343485551),c=c+d>>>0,l=l+T>>>0,f=f+_>>>0,h=h+E>>>0}return r.endian([c,l,f,h])})._ff=function(t,e,n,r,o,i,s){var u=t+(e&n|~e&r)+(o>>>0)+s;return(u<<i|u>>>32-i)+e},u._gg=function(t,e,n,r,o,i,s){var u=t+(e&r|n&~r)+(o>>>0)+s;return(u<<i|u>>>32-i)+e},u._hh=function(t,e,n,r,o,i,s){var u=t+(e^n^r)+(o>>>0)+s;return(u<<i|u>>>32-i)+e},u._ii=function(t,e,n,r,o,i,s){var u=t+(n^(e|~r))+(o>>>0)+s;return(u<<i|u>>>32-i)+e},u._blocksize=16,u._digestsize=16,t.exports=function(t,e){if(null==t)throw new Error("Illegal argument "+t);var n=r.wordsToBytes(u(t,e));return e&&e.asBytes?n:e&&e.asString?s.bytesToString(n):r.bytesToHex(n)}},300:(t,e)=>{"use strict";var n=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==n)return n;throw new Error("unable to locate global object")}();t.exports=e=n.fetch,n.fetch&&(e.default=n.fetch.bind(n)),e.Headers=n.Headers,e.Request=n.Request,e.Response=n.Response},134:t=>{t.exports=class{constructor(t){this.isValid=t}}},865:(t,e,n)=>{const{errorCode:r}=n(466),{KHQRResponse:o}=n(123),i=n(670);t.exports=class extends i{constructor(t,e){if(""==e||null==e||null==e)throw o(null,r.CRC_TAG_REQUIRED);if(e.length>4)throw o(null,r.CRC_LENGTH_INVALID);super(t,e)}}},775:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);class u extends s{constructor(t,e){if(e.length>r.INVALID_LENGTH.BILL_NUMBER||""==e)throw i(null,o.BILL_NUMBER_LENGTH_INVALID);super(t,e)}}class a extends s{constructor(t,e){if(e.length>r.INVALID_LENGTH.STORE_LABEL||""==e)throw i(null,o.STORE_LABEL_LENGTH_INVALID);super(t,e)}}class c extends s{constructor(t,e){if(e.length>r.INVALID_LENGTH.TERMINAL_LABEL||""==e)throw i(null,o.TERMINAL_LABEL_LENGTH_INVALID);super(t,e)}}class l extends s{constructor(t,e){if(e.length>r.INVALID_LENGTH.MOBILE_NUMBER||""==e)throw i(null,o.MOBILE_NUMBER_LENGTH_INVALID);super(t,e)}}t.exports=class extends s{constructor(t,e){null==e&&(e={billNumberInput:null,mobileNumberInput:null,storeLabelInput:null,terminalLabelInput:null});const n=e.billNumber,o=e.mobileNumber,i=e.storeLabel,s=e.terminalLabel;let f,h,p,g,I="";null!=n&&(f=new u(r.BILLNUMBER_TAG,n),I+=f.toString()),null!=o&&(h=new l(r.ADDITIONAL_DATA_FIELD_MOBILE_NUMBER,o),I+=h.toString()),null!=i&&(p=new a(r.STORELABEL_TAG,i),I+=p.toString()),null!=s&&(g=new c(r.TERMINAL_TAG,s),I+=g.toString()),super(t,I),this.billNumber=f,this.mobileNumber=h,this.storeLabel=p,this.terminalLabel=g,this.data={billNumber:f,mobileNumber:h,storeLabel:p,terminalLabel:g}}}},856:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);t.exports=class extends s{constructor(t,e){if(""==e||null==e||null==e)throw i(null,o.COUNTRY_CODE_TAG_REQUIRED);if(e.length>r.INVALID_LENGTH.COUNTRY_CODE)throw i(null,o.COUNTRY_CODE_LENGTH_INVALID);super(t,e)}}},689:(t,e,n)=>{const r=n(670),{emv:o,errorCode:i}=n(466),{KHQRResponse:s}=n(123);class u extends r{constructor(t,e){if(""==e||null==e||null==e)throw s(null,i.BAKONG_ACCOUNT_ID_REQUIRED);const n=e.split("@");if(e.length>o.INVALID_LENGTH.BAKONG_ACCOUNT)throw s(null,i.BAKONG_ACCOUNT_ID_LENGTH_INVALID);if(n.length<2)throw s(null,i.BAKONG_ACCOUNT_ID_INVALID);super(t,e)}}class a extends r{constructor(t,e){if(e.length>o.INVALID_LENGTH.ACCOUNT_INFORMATION)throw s(null,i.ACCOUNT_INFORMATION_LENGTH_INVALID);super(t,e)}}class c extends r{constructor(t,e){if(""==e||null==e||null==e)throw s(null,i.MERCHANT_ID_REQUIRED);if(e.length>o.INVALID_LENGTH.MERCHANT_ID)throw s(null,i.MERCHANT_ID_LENGTH_INVALID);super(t,e)}}class l extends r{constructor(t,e){if(""==e||null==e||null==e)throw s(null,i.ACQUIRING_BANK_REQUIRED);if(e.length>o.INVALID_LENGTH.ACQUIRING_BANK)throw s(null,i.ACQUIRING_BANK_LENGTH_INVALID);super(t,e)}}t.exports=class extends r{constructor(t,e){if(null==e)throw s(null,i.MERCHANT_TYPE_REQUIRED);null==e&&(e={bakongAccountID:null,merchantID:null,acquiringBank:null,accountInformation:null});const n=e.bakongAccountID,r=e.merchantID,f=e.acquiringBank,h=e.isMerchant,p=e.accountInformation,g=new u(o.BAKONG_ACCOUNT_IDENTIFIER,n);let I=g.toString();if(h){const e=new c(o.MERCHANT_ACCOUNT_INFORMATION_MERCHANT_ID,r),n=new l(o.MERCHANT_ACCOUNT_INFORMATION_ACQUIRING_BANK,f);null!=r&&(I+=e.toString()),null!=f&&(I+=n.toString()),super(t,I),this.merchantID=e,this.acquiringBank=n,this.data={bakongAccountID:g,merchantID:e,acquiringBank:n}}else null!=p&&(I+=new a(o.INDIVIDUAL_ACCOUNT_INFORMATION,p).toString()),null!=f&&(I+=new l(o.MERCHANT_ACCOUNT_INFORMATION_ACQUIRING_BANK,f).toString()),super(t,I),this.accountInformation=p,this.data={bakongAccountID:g,accountInformation:p};this.bakongAccountID=g}}},10:(t,e,n)=>{const r=n(958),o=n(575),i=n(865),s=n(689),u=n(453),a=n(856),c=n(224),l=n(944),f=n(273),h=n(734),p=n(469),g=n(775);t.exports={PointOfInitiationMethod:r,PayloadFormatIndicator:o,CRC:i,GlobalUnqiueIdentifier:s,TransactionCurrency:u,CountryCode:a,MerchantCity:c,MerchantCategoryCode:l,TransactionAmount:f,MerchantName:h,TimeStamp:p,AdditionalData:g}},944:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);t.exports=class extends s{constructor(t,e){if(""==e||null==e||null==e)throw i(null,o.MERCHANT_CATEGORY_TAG_REQUIRED);if(e.length>r.INVALID_LENGTH.MERCHANT_CATEGORY_CODE)throw i(null,o.MERCHANT_CODE_LENGTH_INVALID);super(t,e)}}},224:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);t.exports=class extends s{constructor(t,e){if(""==e||null==e||null==e)throw i(null,o.MERCHANT_CITY_TAG_REQUIRED);if(e.length>r.INVALID_LENGTH.MERCHANT_CITY)throw i(null,o.MERCHANT_CITY_LENGTH_INVALID);super(t,e)}}},734:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);t.exports=class extends s{constructor(t,e){if(""==e||null==e||null==e)throw i(null,o.MERCHANT_NAME_REQUIRED);if(e.length>r.INVALID_LENGTH.MERCHANT_NAME)throw i(null,o.MERCHANT_NAME_LENGTH_INVALID);super(t,e)}}},575:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);t.exports=class extends s{constructor(t,e){if(""==e||null==e||null==e)throw i(null,o.PAYLOAD_FORMAT_INDICATOR_TAG_REQUIRED);if(e.length>2)throw i(null,o.PAYLOAD_FORMAT_INDICATOR_LENGTH_INVALID);super(t,e)}}},958:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);t.exports=class extends s{constructor(t,e){if(e.length>2)throw i(null,o.POINT_INITIATION_LENGTH_INVALID);super(t,e)}}},469:(t,e,n)=>{const r=n(670);class o extends r{constructor(t,e){super(t,e)}}t.exports=class extends r{constructor(t){const e=Math.floor(Date.now());super(t,new o("00",e).toString())}}},273:(t,e,n)=>{const{emv:r,errorCode:o}=n(466),{KHQRResponse:i}=n(123),s=n(670);t.exports=class extends s{constructor(t,e){if(String(e).length>r.INVALID_LENGTH.AMOUNT||String(e).includes("-")||""==e||null==e||null==e)throw i(null,o.TRANSACTION_AMOUNT_INVALID);super(t,e)}}},453:(t,e,n)=>{const{emv:r,errorCode:o,khqrData:i}=n(466),{KHQRResponse:s}=n(123),u=n(670);t.exports=class extends u{constructor(t,e){if(""==e||null==e||null==e)throw s(null,o.CURRENCY_TYPE_REQUIRED);if(e.length>3)throw s(null,o.TRANSACTION_CURRENCY_LENGTH_INVALID);if(![i.currency.khr,i.currency.usd].includes(parseInt(e)))throw s(null,o.UNSUPPORTED_CURRENCY);super(t,e)}}},699:(t,e,n)=>{const{emv:r}=n(466);t.exports={input:[{tag:"29",data:{bakongAccountID:null,accountInformation:null}},{tag:"30",data:{bakongAccountID:null,merchantID:null,acquiringBank:null}},{tag:"62",data:{billNumber:null,mobileNumber:null,storeLabel:null,terminalLabel:null}}],compare:[{tag:"29",subTag:r.BAKONG_ACCOUNT_IDENTIFIER,name:"bakongAccountID"},{tag:"29",subTag:r.MERCHANT_ACCOUNT_INFORMATION_MERCHANT_ID,name:"merchantID"},{tag:"29",subTag:r.MERCHANT_ACCOUNT_INFORMATION_ACQUIRING_BANK,name:"acquiringBank"},{tag:"62",subTag:r.BILLNUMBER_TAG,name:"billNumber"},{tag:"62",subTag:r.ADDITIONAL_DATA_FIELD_MOBILE_NUMBER,name:"mobileNumber"},{tag:"62",subTag:r.STORELABEL_TAG,name:"storeLabel"},{tag:"62",subTag:r.TERMINAL_TAG,name:"terminalLabel"}]}},710:(t,e,n)=>{const{PointOfInitiationMethod:r,PayloadFormatIndicator:o,TransactionCurrency:i,MerchantCategoryCode:s,CountryCode:u,MerchantCity:a,TransactionAmount:c,MerchantName:l,TimeStamp:f,AdditionalData:h,CRC:p,GlobalUnqiueIdentifier:g}=n(10);t.exports=[{tag:"00",type:"payloadFormatIndicator",required:!0,instance:o},{tag:"01",type:"pointofInitiationMethod",required:!1,instance:r},{sub:!0,tag:"29",type:"globalUnqiueIdentifier",required:!0,instance:g},{tag:"52",type:"merchantCategoryCode",required:!0,instance:s},{tag:"53",type:"transactionCurrency",required:!0,instance:i},{tag:"54",type:"transactionAmount",required:!1,instance:c},{tag:"58",type:"countryCode",required:!0,instance:u},{tag:"59",type:"merchantName",required:!0,instance:l},{tag:"60",type:"merchantCity",required:!0,instance:a},{tag:"62",sub:!0,type:"additionalData",required:!1,instance:h},{tag:"99",type:"timestamp",required:!1,instance:f},{tag:"63",type:"crc",required:!0,instance:p}]},730:t=>{t.exports={BAKONG_ACCOUNT_ID_REQUIRED:{code:1,message:"Bakong Account ID cannot be null or empty"},MERCHANT_NAME_REQUIRED:{code:2,message:"Merchant name cannot be null or empty"},BAKONG_ACCOUNT_ID_INVALID:{code:3,message:"Bakong Account ID is invalid"},TRANSACTION_AMOUNT_INVALID:{code:4,message:"Amount is invalid"},MERCHANT_TYPE_REQUIRED:{code:5,message:"Merchant type cannot be null or empty"},BAKONG_ACCOUNT_ID_LENGTH_INVALID:{code:6,message:"Bakong Account ID Length is Invalid"},MERCHANT_NAME_LENGTH_INVALID:{code:7,message:"Merchant Name Length is invalid"},KHQR_INVALID:{code:8,message:"KHQR provided is invalid"},CURRENCY_TYPE_REQUIRED:{code:9,message:"Currency type cannot be null or empty"},BILL_NUMBER_LENGTH_INVALID:{code:10,message:"Bill Name Length is invalid"},STORE_LABEL_LENGTH_INVALID:{code:11,message:"Store Label Length is invalid"},TERMINAL_LABEL_LENGTH_INVALID:{code:12,message:"Terminal Label Length is invalid"},CONNECTION_TIMEOUT:{code:13,message:"Cannot reach Bakong Open API service. Please check internet connection"},INVALID_DEEP_LINK_SOURCE_INFO:{code:14,message:"Source Info for Deep Link is invalid"},INTERNAL_SREVER:{code:15,message:"Internal server error"},PAYLOAD_FORMAT_INDICATOR_LENGTH_INVALID:{code:16,message:"Payload Format indicator Length is invalid"},POINT_INITIATION_LENGTH_INVALID:{code:17,message:"Point of initiation Length is invalid"},MERCHANT_CODE_LENGTH_INVALID:{code:18,message:"Merchant code Length is invalid"},TRANSACTION_CURRENCY_LENGTH_INVALID:{code:19,message:"Transaction currency Length is invalid"},COUNTRY_CODE_LENGTH_INVALID:{code:20,message:"Country code Length is invalid"},MERCHANT_CITY_LENGTH_INVALID:{code:21,message:"Merchant city Length is invalid"},CRC_LENGTH_INVALID:{code:22,message:"CRC Length is invalid"},PAYLOAD_FORMAT_INDICATOR_TAG_REQUIRED:{code:23,message:"Payload format indicator tag required"},CRC_TAG_REQUIRED:{code:24,message:"CRC tag required"},MERCHANT_CATEGORY_TAG_REQUIRED:{code:25,message:"Merchant category tag required"},COUNTRY_CODE_TAG_REQUIRED:{code:26,message:"Country Code cannot be null or empty"},MERCHANT_CITY_TAG_REQUIRED:{code:27,message:"Merchant City cannot be null or empty"},UNSUPPORTED_CURRENCY:{code:28,message:"Unsupported currency"},INVALID_DEEP_LINK_URL:{code:29,message:"Deep Link URL is not valid"},MERCHANT_ID_REQUIRED:{code:30,message:"Merchant ID cannot be null or empty"},ACQUIRING_BANK_REQUIRED:{code:31,message:"Acquiring Bank cannot be null or empty"},MERCHANT_ID_LENGTH_INVALID:{code:32,message:"Merchant ID Length is invalid"},ACQUIRING_BANK_LENGTH_INVALID:{code:33,message:"Acquiring Bank Length is invalid"},MOBILE_NUMBER_LENGTH_INVALID:{code:34,message:"Mobile Number Length is invalid"},ACCOUNT_INFORMATION_LENGTH_INVALID:{code:35,message:"Account Information Length is invalid"},TAG_NOT_IN_ORDER:{code:35,message:"Tag is not in order"}}},466:(t,e,n)=>{const r=n(8),o=n(730);t.exports={emv:{PAYLOAD_FORMAT_INDICATOR:"00",DEFAULT_PAYLOAD_FORMAT_INDICATOR:"01",POINT_OF_INITIATION_METHOD:"01",STATIC_QR:"11",DYNAMIC_QR:"12",MERCHANT_ACCOUNT_INFORMATION_INDIVIDUAL:"29",MERCHANT_ACCOUNT_INFORMATION_MERCHANT:"30",BAKONG_ACCOUNT_IDENTIFIER:"00",MERCHANT_ACCOUNT_INFORMATION_MERCHANT_ID:"01",INDIVIDUAL_ACCOUNT_INFORMATION:"01",MERCHANT_ACCOUNT_INFORMATION_ACQUIRING_BANK:"02",MERCHANT_CATEGORY_CODE:"52",DEFAULT_MERCHANT_CATEGORY_CODE:"5999",TRANSACTION_CURRENCY:"53",TRANSACTION_AMOUNT:"54",DEFAULT_TRANSACTION_AMOUNT:"0",COUNTRY_CODE:"58",DEFAULT_COUNTRY_CODE:"KH",MERCHANT_NAME:"59",MERCHANT_CITY:"60",DEFAULT_MERCHANT_CITY:"Phnom Penh",CRC:"63",CRC_LENGTH:"04",ADDITIONAL_DATA_TAG:"62",BILLNUMBER_TAG:"01",ADDITIONAL_DATA_FIELD_MOBILE_NUMBER:"02",STORELABEL_TAG:"03",TERMINAL_TAG:"07",TIMESTAMP_TAG:"99",INVALID_LENGTH:{KHQR:12,MERCHANT_NAME:24,BAKONG_ACCOUNT:32,AMOUNT:13,COUNTRY_CODE:3,MERCHANT_CATEGORY_CODE:4,MERCHANT_CITY:15,TIMESTAMP:13,TRANSACTION_AMOUNT:14,TRANSACTION_CURRENCY:3,TIMESTAMP:13,BILL_NUMBER:24,STORE_LABEL:24,TERMINAL_LABEL:24,MERCHANT_ID:32,ACQUIRING_BANK:32,MOBILE_NUMBER:12,ACCOUNT_INFORMATION:32}},errorCode:o,khqrData:r}},8:t=>{t.exports={currency:{usd:840,khr:116},merchantType:{merchant:"merchant",individual:"individual"}}},270:(t,e,n)=>{const{errorCode:r}=n(466),o=n(699),i=n(710),s=n(215),{KHQRResponse:u}=n(123);t.exports=function(t){const e=i.map((t=>t.tag)),n=i.filter((t=>1==t.sub)).map((t=>t.tag));let r=i.filter((t=>1==t.required)).map((t=>t.tag));const u=o.input,a=o.compare;let c={},l=null,f="";for(;t;){sliceTagObject=s(t);let{tag:n,value:o,slicedString:i}=sliceTagObject;if(n==f)break;"30"==n?(l="30",n="29"):"29"==n&&(l="29"),e.includes(n)&&(c[n]=o,r=r.filter((t=>t!=n))),t=i,f=n}let h={merchantType:l};return u.map((t=>t.data)).forEach((t=>h={...h,...t})),i.forEach((t=>{const e=t.tag,r=i.find((t=>t.tag==e));let o=null==c[e]?null:c[e],l=o;if(n.includes(e)){const t=((t,e)=>t.find((t=>t.tag==e)))(u,e).data;for(;o;){cutsubstring=s(o);const n=cutsubstring.tag,r=cutsubstring.value,i=cutsubstring.slicedString;let u=a.filter((t=>t.tag==e)).find((t=>t.subTag==n));null!=u&&(u=u.name,t[u]=r,l=t),o=i}h={...h,...l};try{new r.instance(e,l)}catch(t){}}else h[r.type]=o,"99"==e&&null==o&&(h[r.type]=null)})),h}},262:(t,e,n)=>{const{errorCode:r}=n(466),o=n(699),i=n(710),s=n(215),{KHQRResponse:u}=n(123),a=(t,e)=>t.find((t=>t.tag==e));t.exports=function(t){const e=i.map((t=>t.tag)),n=i.filter((t=>1==t.sub)).map((t=>t.tag));let c=i.filter((t=>1==t.required)).map((t=>t.tag));const l=o.input,f=o.compare;let h=[],p="individual",g="";for(;t;){sliceTagObject=s(t);let{tag:n,value:r,slicedString:o}=sliceTagObject;if(n==g)break;"30"==n&&(p="merchant",n="29"),e.includes(n)&&(h.push({tag:n,value:r}),c=c.filter((t=>t!=n))),t=o,g=n}if(0!=c.length){const t=c[0];new(0,a(i,t).instance)(t,null)}if(!(I=h.map((t=>parseInt(t.tag))).splice(-1,1)).slice(1).every(((t,e)=>t>I[e])))throw u(null,r.TAG_NOT_IN_ORDER);var I;let A={merchantType:p};return l.map((t=>t.data)).forEach((t=>A={...A,...t})),h.forEach((t=>{const e=t.tag,r=i.find((t=>t.tag==e));let o=t.value,u=o;if(n.includes(e)){const t=a(l,e).data;for(;o;){cutsubstring=s(o);const n=cutsubstring.tag,r=cutsubstring.value,i=cutsubstring.slicedString;let a=f.filter((t=>t.tag==e)).find((t=>t.subTag==n));null!=a&&(a=a.name,t[a]=r,u=t),o=i}A={...A,...u},new r.instance(e,u)}else{const t=new r.instance(e,u);A[r.type]=t.value}})),A}},399:(t,e,n)=>{const{emv:r,khqrData:o,errorCode:i}=n(466),s=n(967),{PointOfInitiationMethod:u,PayloadFormatIndicator:a,GlobalUnqiueIdentifier:c,TransactionCurrency:l,CountryCode:f,MerchantCity:h,MerchantCategoryCode:p,TransactionAmount:g,MerchantName:I,TimeStamp:A,AdditionalData:N}=n(10),{KHQRResponse:d}=n(123);t.exports=function(t,e){let n={bakongAccountID:t.bakongAccountID,isMerchant:!1};n="merchant"==e?{bakongAccountID:t.bakongAccountID,merchantID:t.merchantID,acquiringBank:t.acquiringBank,isMerchant:!0}:{bakongAccountID:t.bakongAccountID,accountInformation:t.accountInformation,acquiringBank:t.acquiringBank,isMerchant:!1};const T={billNumber:t.billNumber,mobileNumber:t.mobileNumber,storeLabel:t.storeLabel,terminalLabel:t.terminalLabel};try{const _=t.amount,E=new a(r.PAYLOAD_FORMAT_INDICATOR,r.DEFAULT_PAYLOAD_FORMAT_INDICATOR);let y=r.DYNAMIC_QR;null!=_&&0!=_||(y=r.STATIC_QR);const R=new u(r.POINT_OF_INITIATION_METHOD,y);let C=r.MERCHANT_ACCOUNT_INFORMATION_INDIVIDUAL;"merchant"==e&&(C=r.MERCHANT_ACCOUNT_INFORMATION_MERCHANT);const m=new c(C,n),b=[E,R,m,new p(r.MERCHANT_CATEGORY_CODE,r.DEFAULT_MERCHANT_CATEGORY_CODE),new l(r.TRANSACTION_CURRENCY,t.currency)];if(null!=_&&0!=_){let e=t.amount;if(t.currency==o.currency.khr){if(e%1!=0)throw d(null,i.TRANSACTION_AMOUNT_INVALID);e=Math.round(e)}else{const t=String(e).split(".")[1];if(null!=t&&t.length>2)throw d(null,i.TRANSACTION_AMOUNT_INVALID);null!=t&&(e=parseFloat(e).toFixed(2))}const n=new g(r.TRANSACTION_AMOUNT,e);b.push(n)}const O=new f(r.COUNTRY_CODE,r.DEFAULT_COUNTRY_CODE);b.push(O);const L=new I(r.MERCHANT_NAME,t.merchantName);b.push(L);const w=new h(r.MERCHANT_CITY,t.merchantCity);let D;b.push(w),Object.values(T).every((t=>null==t||null==t||""==t))||(D=new N(r.ADDITIONAL_DATA_TAG,T),b.push(D));const B=new A(r.TIMESTAMP_TAG);b.push(B);let U="";for(let t=0;t<b.length;t++)U+=b[t].toString();return khqr=U+r.CRC+r.CRC_LENGTH,khqr+=s(khqr),khqr}catch(t){return t}}},194:(t,e,n)=>{const{KHQRResponse:r}=n(123),{errorCode:o,emv:i}=n(466),s=n(300);t.exports=async function(t,e){if(e.length>i.INVALID_LENGTH.BAKONG_ACCOUNT)throw r(null,o.BAKONG_ACCOUNT_ID_INVALID);let n;try{n=await s(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({accountId:e}),timeout:45e3});const r=n.clone();JSON.parse(await r.text())}catch(c){throw r(null,o.CONNECTION_TIMEOUT)}if(null==n)throw r(null,o.CONNECTION_TIMEOUT);if(404==n.status||500==n.status)throw r(null,o.CONNECTION_TIMEOUT);const u=await n.json();if(404==u.status||500==u.status)throw r(null,o.CONNECTION_TIMEOUT);const a=u.responseCode,c=u.errorCode;if(11==c)return{bakongAccountExisted:!1};if(12==c)throw r(null,o.BAKONG_ACCOUNT_ID_INVALID);return 0==a?{bakongAccountExisted:!0}:{bakongAccountExisted:!1}}},967:(t,e,n)=>{const r=n(764).lW;var o=[0,4129,8258,12387,16516,20645,24774,28903,33032,37161,41290,45419,49548,53677,57806,61935,4657,528,12915,8786,21173,17044,29431,25302,37689,33560,45947,41818,54205,50076,62463,58334,9314,13379,1056,5121,25830,29895,17572,21637,42346,46411,34088,38153,58862,62927,50604,54669,13907,9842,5649,1584,30423,26358,22165,18100,46939,42874,38681,34616,63455,59390,55197,51132,18628,22757,26758,30887,2112,6241,10242,14371,51660,55789,59790,63919,35144,39273,43274,47403,23285,19156,31415,27286,6769,2640,14899,10770,56317,52188,64447,60318,39801,35672,47931,43802,27814,31879,19684,23749,11298,15363,3168,7233,60846,64911,52716,56781,44330,48395,36200,40265,32407,28342,24277,20212,15891,11826,7761,3696,65439,61374,57309,53244,48923,44858,40793,36728,37256,33193,45514,41451,53516,49453,61774,57711,4224,161,12482,8419,20484,16421,28742,24679,33721,37784,41979,46042,49981,54044,58239,62302,689,4752,8947,13010,16949,21012,25207,29270,46570,42443,38312,34185,62830,58703,54572,50445,13538,9411,5280,1153,29798,25671,21540,17413,42971,47098,34713,38840,59231,63358,50973,55100,9939,14066,1681,5808,26199,30326,17941,22068,55628,51565,63758,59695,39368,35305,47498,43435,22596,18533,30726,26663,6336,2273,14466,10403,52093,56156,60223,64286,35833,39896,43963,48026,19061,23124,27191,31254,2801,6864,10931,14994,64814,60687,56684,52557,48554,44427,40424,36297,31782,27655,23652,19525,15522,11395,7392,3265,61215,65342,53085,57212,44955,49082,36825,40952,28183,32310,20053,24180,11923,16050,3793,7920];t.exports=function(t){var e,n,i=65535;for(t=r.from(t),n=0;n<t.length;n++)c=t[n],e=255&(c^i>>8),i=o[e]^i<<8;return function(t){let e=t.toString(16).toUpperCase();e.length<4&&(e="0"*(4-e.length)+e);const n=4-e.length;return e.length<4&&(e="0"*n+e),e}(65535&(0^i))}},215:t=>{t.exports=function(t){const e=t.slice(0,2),n=parseInt(t.slice(2,4));return{tag:e,value:t.slice(4,4+n),slicedString:t.slice(4+n)}}},914:(t,e,n)=>{const r=n(300),{errorCode:o}=n(466),{KHQRResponse:i}=n(123);t.exports={isValidLink:function(t){try{if("/v1/generate_deeplink_by_qr"!=new URL(t).pathname)return!1}catch(t){return!1}return!0},callDeepLinkAPI:async function(t,e){let n=await r(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),timeout:45e3});try{JSON.parse(n.text())}catch(t){throw i(null,o.CONNECTION_TIMEOUT)}if(null==n)throw i(null,o.CONNECTION_TIMEOUT);return n},SourceInfo:class{constructor(t,e,n){this.appIconUrl=t||null,this.appName=e||null,this.appDeepLinkCallback=n||null}}}},138:(t,e,n)=>{const{khqrData:r,errorCode:o,emv:i}=n(466),s=n(967),u=n(134),{isValidLink:a,callDeepLinkAPI:c,SourceInfo:l}=n(914),{KHQRDeepLinkData:f,KHQRResponse:h}=n(123),{IndividualInfo:p,MerchantInfo:g}=n(648),I=n(399),A=n(270),N=n(568),d=n(262),T=n(194);t.exports={BakongKHQR:class{generateIndividual(t){const e=I(t,r.merchantType.individual);if(null!=e.status)return e;const n={qr:e,md5:N(e)};return h(n,null)}generateMerchant(t){const e=I(t,r.merchantType.merchant);if(null!=e.status)return e;const n={qr:e,md5:N(e)};return h(n,null)}static decode(t){const e=A(t);return h(e,null)}static verify(t){const e=t.slice(-4),n=t.slice(0,-4),r=s(n)==e,a=new u(r);try{if(!a.isValid||t.length<i.INVALID_LENGTH.KHQR)throw h(null,o.KHQR_INVALID);return d(t),new u(!0)}catch(t){return new u(!1)}}static async generateDeepLink(t,e,n){if(!a(t))return h(null,o.INVALID_DEEP_LINK_URL);if(!this.verify(e).isValid)return h(null,o.KHQR_INVALID);if(n&&(!n.appIconUrl||!n.appName||!n.appDeepLinkCallback))return h(null,o.INVALID_DEEP_LINK_SOURCE_INFO);let r;try{if(r=await c(t,{qr:e}),404==r.status||500==r.status)return h(null,o.CONNECTION_TIMEOUT);const n=await r.json();if(5==n.errorCode)return h(null,o.INVALID_DEEP_LINK_SOURCE_INFO);if(4==n.errorCode)return h(null,o.INTERNAL_SERVER_ERROR);const i=new f(n.data.shortLink);return h(i.getData(),null)}catch(t){return t}}static async checkBakongAccount(t,e){try{const n=await T(t,e);return h(n,null)}catch(t){return t}}},khqrData:r,SourceInfo:l,IndividualInfo:p,MerchantInfo:g}},123:(t,e,n)=>{const r=n(565);t.exports=r},648:(t,e,n)=>{const{khqrData:r}=n(466);class o{constructor(t,e,n,o={}){if(this.isObject(t)||this.isObject(e)||this.isObject(n))throw"bakongAccountID, merchantName or merchantCity must be a string";this.bakongAccountID=t,this.accountInformation=o.accountInformation,this.acquiringBank=o.acquiringBank,this.currency=null==o.currency?r.currency.khr:o.currency,this.amount=o.amount,this.merchantName=e,this.merchantCity=n,this.billNumber=o.billNumber,this.storeLabel=o.storeLabel,this.terminalLabel=o.terminalLabel,this.mobileNumber=o.mobileNumber}isObject(t){return t instanceof Object}}t.exports={IndividualInfo:o,MerchantInfo:class extends o{constructor(t,e,n,r,o,i={}){if(super(t,e,n,i),this.isObject(r)||this.isObject(o))throw"merchantID and acquiringBank must be a string";this.amount=i.amount,this.merchantID=r,this.acquiringBank=o}isObject(t){return t instanceof Object}}}},565:(t,e,n)=>{const r=n(568);t.exports={KHQRData:class{constructor(t){this.qr=t,this.md5=r(t)}getData(){return{qr:this.qr,md5:this.md5}}},KHQRDeepLinkData:class{constructor(t){this.shortLink=t}getData(){return{shortLink:this.shortLink}}},KHQRResponse:function(t,e){const n=null==e;return{status:{code:n?0:1,errorCode:n?null:e.code,message:n?null:e.message},data:t}}}},670:t=>{t.exports=class{constructor(t,e){this.tag=t,this.value=e;const n=String(e).length;this.length=n<10?`0${n}`:String(n)}toString(){return`${this.tag}${this.length}${this.value}`}}}},e={},function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}(138);var t,e}));